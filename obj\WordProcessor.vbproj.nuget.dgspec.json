{"format": 1, "restore": {"D:\\Documents\\Desktop\\Trae\\WordProcessor.vbproj": {}}, "projects": {"D:\\Documents\\Desktop\\Trae\\WordProcessor.vbproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Documents\\Desktop\\Trae\\WordProcessor.vbproj", "projectName": "WordProcessor", "projectPath": "D:\\Documents\\Desktop\\Trae\\WordProcessor.vbproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Documents\\Desktop\\Trae\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Microsoft.Office.Interop.Word": {"target": "Package", "version": "[15.0.4797.1004, )"}, "System.Drawing.Common": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}}}