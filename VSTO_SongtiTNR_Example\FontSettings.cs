using System;

namespace WordFontFormatterAddIn
{
    /// <summary>
    /// 字体设置数据类 - 替代 VBA 中的字符串解析
    /// </summary>
    public class FontSettings
    {
        public string NumberFontName { get; set; } = "宋体";
        public double NumberFontSize { get; set; } = 10.5;
        public bool NumberIsBold { get; set; } = false;
        
        public string TextFontName { get; set; } = "宋体";
        public double TextFontSize { get; set; } = 10.5;
        public bool TextIsBold { get; set; } = false;

        /// <summary>
        /// 验证设置是否有效
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrEmpty(NumberFontName) && 
                   !string.IsNullOrEmpty(TextFontName) &&
                   NumberFontSize > 0 && 
                   TextFontSize > 0;
        }

        /// <summary>
        /// 获取设置摘要
        /// </summary>
        public string GetSummary()
        {
            return $"编号：{NumberFontName} {NumberFontSize}pt {(NumberIsBold ? "粗体" : "常规")}\n" +
                   $"正文：{TextFontName} {TextFontSize}pt {(TextIsBold ? "粗体" : "常规")}";
        }
    }
}