Word格式化工具 - 编译安装指南
=====================================

问题：系统找不到指定的文件
原因：缺少C++编译器或Visual Studio环境未正确设置

解决方案：

方案一：使用Visual Studio Developer Command Prompt（推荐）
--------------------------------------------------------
1. 按Win键，搜索"Developer Command Prompt"
2. 或搜索"x64 Native Tools Command Prompt for VS"
3. 右键选择"以管理员身份运行"
4. 在命令提示符中切换到代码目录：
   cd /d "你的代码目录路径"
5. 运行编译脚本：
   quick_compile.bat

方案二：安装MinGW-w64（简单）
---------------------------
1. 打开PowerShell（管理员）
2. 运行：winget install mingw
3. 重启命令提示符
4. 运行：compile_simple.bat

方案三：安装Visual Studio Community（完整）
-----------------------------------------
1. 访问：https://visualstudio.microsoft.com/downloads/
2. 下载"Visual Studio Community"（免费）
3. 安装时选择"使用C++的桌面开发"工作负载
4. 安装完成后运行：build_fixed.bat

方案四：手动编译（如果有编译器）
-----------------------------
在有cl.exe的环境中运行：

cl /EHsc /std:c++17 /utf-8 /D_WIN32_DCOM /DUNICODE /D_UNICODE /D_CRT_SECURE_NO_WARNINGS WordFormatter_Clean.cpp /link ole32.lib oleaut32.lib uuid.lib user32.lib kernel32.lib /OUT:WordFormatter.exe

常见问题：
---------
Q: 提示找不到MSWORD.OLB
A: 检查Office安装路径，修改代码中的路径

Q: 编译成功但运行出错
A: 确保Word正在运行且已选择文本

Q: 中文显示乱码
A: 在命令提示符中运行：chcp 65001

使用方法：
---------
1. 编译成功后会生成 WordFormatter.exe
2. 打开Word文档，选择需要格式化的文本
3. 双击运行 WordFormatter.exe
4. 按提示输入参数：宋体,10.5,否,宋体,10.5,否

参数说明：
编号字体,编号字号,编号粗体,正文字体,正文字号,正文粗体

推荐使用方案一，最简单快捷！