' === 第三部分：智能列宽调整 + 设置边框 ===
With mytable
    ' 先按内容自动调整，获取最小宽度
    .AutoFitBehavior wdAutoFitContent
    
    ' 计算当前总宽度和页面可用宽度
    Dim curTotal As Single, newTotal As Single, scale As Single
    Dim i As Long, j As Long
    
    curTotal = 0
    For i = 1 To .Columns.Count
        curTotal = curTotal + .Columns(i).Width
    Next i
    
    ' 获取页面可用宽度
    With .Range.Document.PageSetup
        newTotal = .PageWidth - .LeftMargin - .RightMargin
    End With
    
    ' 如果当前宽度小于页面宽度，进行智能均衡调整
    If curTotal > 0 And curTotal < newTotal Then
        ' 根据列数进行智能分配
        Select Case .Columns.Count
            Case 2
                ' 两列表格：检查第一列是否为序号列
                Dim firstColMaxLen As Long
                firstColMaxLen = 0
                For i = 1 To .Rows.Count
                    Dim cellText As String
                    cellText = Trim(Left(.Cell(i, 1).Range.Text, Len(.Cell(i, 1).Range.Text) - 2))
                    If Len(cellText) > firstColMaxLen Then firstColMaxLen = Len(cellText)
                Next i
                
                If firstColMaxLen <= 6 Then
                    ' 第一列是序号列
                    .Columns(1).PreferredWidthType = wdPreferredWidthPercent
                    .Columns(1).PreferredWidth = 25
                    .Columns(2).PreferredWidthType = wdPreferredWidthPercent
                    .Columns(2).PreferredWidth = 75
                Else
                    ' 正常两列
                    .Columns(1).PreferredWidthType = wdPreferredWidthPercent
                    .Columns(1).PreferredWidth = 45
                    .Columns(2).PreferredWidthType = wdPreferredWidthPercent
                    .Columns(2).PreferredWidth = 55
                End If
                
            Case 3
                ' 三列表格
                .Columns(1).PreferredWidthType = wdPreferredWidthPercent
                .Columns(1).PreferredWidth = 30
                .Columns(2).PreferredWidthType = wdPreferredWidthPercent
                .Columns(2).PreferredWidth = 40
                .Columns(3).PreferredWidthType = wdPreferredWidthPercent
                .Columns(3).PreferredWidth = 30
                
            Case 4
                ' 四列表格：针对您的表格结构优化
                .Columns(1).PreferredWidthType = wdPreferredWidthPercent
                .Columns(1).PreferredWidth = 12  ' 序号/不动产权证号
                .Columns(2).PreferredWidthType = wdPreferredWidthPercent
                .Columns(2).PreferredWidth = 35  ' 地块位置
                .Columns(3).PreferredWidthType = wdPreferredWidthPercent
                .Columns(3).PreferredWidth = 23  ' 账面原值
                .Columns(4).PreferredWidthType = wdPreferredWidthPercent
                .Columns(4).PreferredWidth = 30  ' 现状/备注
                
            Case 5
                ' 五列表格
                .Columns(1).PreferredWidthType = wdPreferredWidthPercent
                .Columns(1).PreferredWidth = 12
                .Columns(2).PreferredWidthType = wdPreferredWidthPercent
                .Columns(2).PreferredWidth = 25
                .Columns(3).PreferredWidthType = wdPreferredWidthPercent
                .Columns(3).PreferredWidth = 22
                .Columns(4).PreferredWidthType = wdPreferredWidthPercent
                .Columns(4).PreferredWidth = 21
                .Columns(5).PreferredWidthType = wdPreferredWidthPercent
                .Columns(5).PreferredWidth = 20
                
            Case Else
                ' 其他列数：等比放大到页面宽度
                If curTotal > 0 Then
                    scale = newTotal / curTotal
                    For i = 1 To .Columns.Count
                        With .Columns(i)
                            .PreferredWidthType = wdPreferredWidthPoints
                            .PreferredWidth = .Width * scale
                        End With
                    Next i
                End If
        End Select
    Else
        ' 如果当前宽度已经接近或超过页面宽度，等比缩放
        If curTotal > 0 Then
            scale = newTotal / curTotal
            For i = 1 To .Columns.Count
                With .Columns(i)
                    .PreferredWidthType = wdPreferredWidthPoints
                    .PreferredWidth = .Width * scale
                End With
            Next i
        End If
    End If
    
    ' 设置表格整体宽度
    .PreferredWidthType = wdPreferredWidthPercent
    .PreferredWidth = 100
    
    ' 设置边框样式
    .Borders(wdBorderLeft).LineStyle = wdLineStyleNone
    .Borders(wdBorderRight).LineStyle = wdLineStyleNone
    
    With .Borders(wdBorderTop)
        .LineStyle = wdLineStyleDouble
        .LineWidth = wdLineWidth050pt
    End With
    
    With .Borders(wdBorderBottom)
        .LineStyle = wdLineStyleDouble
        .LineWidth = wdLineWidth050pt
    End With
    
    With .Borders(wdBorderHorizontal)
        .LineStyle = wdLineStyleSingle
        .LineWidth = wdLineWidth050pt
    End With
    
    With .Borders(wdBorderVertical)
        .LineStyle = wdLineStyleSingle
        .LineWidth = wdLineWidth050pt
    End With
    
    ' 锁定列宽，防止后续自动调整
    .AllowAutoFit = False
End With