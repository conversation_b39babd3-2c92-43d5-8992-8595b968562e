@echo off
echo ========================================
echo Word文档处理工具 发布脚本
echo ========================================
echo.

echo 正在发布独立可执行文件...
dotnet publish WordProcessor.vbproj -c Release -r win-x64 -p:PublishSingleFile=true -p:SelfContained=true -o publish

if errorlevel 1 (
    echo.
    echo 发布失败，尝试不使用SingleFile...
    dotnet publish WordProcessor.vbproj -c Release -r win-x64 -p:SelfContained=true -o publish-multi
    if errorlevel 1 (
        echo 发布仍然失败
        pause
        exit /b 1
    ) else (
        echo.
        echo 发布成功！（多文件版本）
        echo 文件位置: publish-multi\WordProcessor.exe
    )
) else (
    echo.
    echo 发布成功！（单文件版本）
    echo 文件位置: publish\WordProcessor.exe
)

echo.
pause