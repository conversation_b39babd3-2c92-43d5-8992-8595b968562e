@echo off
setlocal enabledelayedexpansion
title WordProcessor Build & Publish

echo ========================================
echo  Word 文档处理工具   编译 / 发布脚本
echo ========================================
echo.

::-------------------------------------------
:: 0. 检查 dotnet
dotnet --version >nul 2>&1 || (
  echo [ERROR] 未找到 .NET SDK，请先安装 https://dotnet.microsoft.com/download
  pause & exit /b 1
)

::-------------------------------------------
:: 1. 解析项目文件（假设目录里只有一个 *.vbproj）
for %%F in (*.vbproj) do set PROJ=%%F
if not defined PROJ (
  echo [ERROR] 当前目录未找到 *.vbproj
  pause & exit /b 1
)
echo ◇ 目标项目: %PROJ%

:: 读取 TargetFramework
for /f "delims=" %%T in ('dotnet msbuild "%PROJ%" -nologo -p:Configuration=Release -getProperty:TargetFramework 2^>nul') do set TF=%%T
if not defined TF set TF=net6.0-windows
echo ◇ TargetFramework: %TF%
echo.

::-------------------------------------------
:: 2. 还原
echo 正在还原 NuGet...
dotnet restore "%PROJ%" || exit /b %errorlevel%

::-------------------------------------------
:: 3. 编译 Release
echo.
echo 正在编译 Release...
dotnet build "%PROJ%" -c Release -p:ContinuousIntegrationBuild=true ^
|| exit /b %errorlevel%

:: 生成路径
set OUT_DIR=bin\Release\%TF%
echo.
echo ==== ✓ Build succeed.
echo     输出: %OUT_DIR%
echo.

::-------------------------------------------
:: 4. 是否发布为独立 exe
choice /m "是否发布为独立可执行文件 (y = 单文件 / s = 分文件 / n = 跳过)" /c ysn
if errorlevel 3 goto :end
if errorlevel 2 set PUB_SINGLE=false&goto :askRID
set PUB_SINGLE=true

:askRID
echo.
choice /m "请选择发布架构 (1 = win‑x64 / 2 = win‑x86)" /c 12
if errorlevel 2 (set RID=win-x86) else set RID=win-x64

echo.
echo 发布中... (RID=%RID%  SingleFile=%PUB_SINGLE%)
dotnet publish "%PROJ%" -c Release -r %RID% ^
  -p:PublishSingleFile=%PUB_SINGLE% ^
  -p:SelfContained=true ^
  -p:PublishTrimmed=false ^
  --no-build || exit /b %errorlevel%

echo.
echo ==== ✓ Publish succeed.
echo     发布文件夹: %OUT_DIR%\%RID%\publish
goto :end

:end
echo.
echo **** 完成 ****
pause
