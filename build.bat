@echo off
chcp 65001 >nul
echo ========================================
echo Word文档处理工具 编译脚本
echo ========================================

echo.
echo 正在检查.NET SDK...
dotnet --version
if errorlevel 1 (
    echo 错误: 未找到.NET SDK，请先安装.NET 6.0 SDK
    pause
    exit /b 1
)

echo.
echo 正在还原NuGet包...
dotnet restore
if errorlevel 1 (
    echo 错误: NuGet包还原失败
    pause
    exit /b 1
)

echo.
echo 正在编译项目...
dotnet build --configuration Release
if errorlevel 1 (
    echo 错误: 项目编译失败
    pause
    exit /b 1
)

echo.
echo 编译成功！
echo 可执行文件位置: bin\Release\net6.0-windows\WordProcessor.exe
echo.

echo 是否要发布为独立可执行文件？(y/n)
set /p choice=
if /i "%choice%"=="y" (
    echo.
    echo 正在发布独立可执行文件...
    dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:PublishTrimmed=false
    if errorlevel 1 (
        echo 错误: 发布失败
        pause
        exit /b 1
    )
    echo.
    echo 发布成功！
    echo 独立可执行文件位置: bin\Release\net6.0-windows\win-x64\publish\WordProcessor.exe
)

echo.
echo 完成！
pause