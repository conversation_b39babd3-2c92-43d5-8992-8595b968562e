@echo off
echo ========================================
echo 数字处理工具 - 独立版编译脚本
echo ========================================
echo.

echo 正在编译独立版项目...
dotnet build NumberProcessorTool.vbproj -c Release

if errorlevel 1 (
    echo.
    echo 编译失败
    pause
    exit /b 1
)

echo.
echo 正在发布独立可执行文件...
dotnet publish NumberProcessorTool.vbproj -c Release -r win-x64 -p:PublishSingleFile=true -p:SelfContained=true -o dist

if errorlevel 1 (
    echo.
    echo 发布失败
    pause
    exit /b 1
) else (
    echo.
    echo 发布成功！
    echo 文件位置: dist\NumberProcessorTool.exe
)

echo.
echo 独立版功能说明：
echo - 转换为万元：将数字转换为万元格式
echo - 添加千分位：为数字添加千分位逗号分隔符
echo - 计算总和：计算文本中所有数字的总和、平均值
echo - 清空：清空输入和输出
echo - 复制结果：将处理结果复制到剪贴板
echo.
echo 使用方法：
echo 1. 在输入框中输入包含数字的文本
echo 2. 点击相应的处理按钮
echo 3. 查看处理结果并可复制使用
echo.
echo 优势：
echo - 无需安装Word
echo - 完全独立运行
echo - 支持批量处理多个数字
echo - 自动识别文本中的数字
echo.
pause