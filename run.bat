@echo off
echo ========================================
echo Word文档处理工具 启动脚本
echo ========================================
echo.

echo 正在检查.NET Runtime...
dotnet --version
if errorlevel 1 (
    echo 错误: 未找到.NET Runtime
    echo 请安装.NET 6.0 Runtime: https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    exit /b 1
)

echo.
echo 正在检查Word...
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Office" >nul 2>&1
if errorlevel 1 (
    echo 警告: 可能未安装Microsoft Office
)

echo.
echo 正在启动程序...
cd /d "%~dp0bin\Release\net6.0-windows"
if exist "WordProcessor.exe" (
    WordProcessor.exe
) else (
    echo 错误: 未找到WordProcessor.exe
    echo 请先编译项目
)

echo.
echo 程序已退出
pause