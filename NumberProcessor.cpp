#include <windows.h>
#include <comdef.h>
#include <iostream>
#include <string>
#include <vector>
#include <regex>
#include <iomanip>
#include <sstream>
#include <locale>
#include <codecvt>

// Word COM接口头文件
#import "C:\\Program Files\\Microsoft Office\\root\\Office16\\MSWORD.OLB" \
    rename("ExitWindows", "WordExitWindows") \
    rename("FindText", "WordFindText")

using namespace Word;
using namespace std;

class WordProcessor {
private:
    Word::_ApplicationPtr wordApp;
    Word::_DocumentPtr activeDoc;
    
public:
    WordProcessor() : wordApp(nullptr), activeDoc(nullptr) {}
    
    ~WordProcessor() {
        if (wordApp) {
            wordApp.Release();
        }
    }
    
    // 初始化Word应用程序连接
    bool InitializeWord() {
        try {
            HRESULT hr = CoInitialize(NULL);
            if (FAILED(hr)) {
                cout << "COM初始化失败" << endl;
                return false;
            }
            
            // 尝试连接到现有的Word实例
            hr = wordApp.GetActiveObject(L"Word.Application");
            if (FAILED(hr)) {
                // 如果没有运行的Word，创建新实例
                hr = wordApp.CreateInstance(L"Word.Application");
                if (FAILED(hr)) {
                    cout << "无法创建Word应用程序实例" << endl;
                    return false;
                }
                wordApp->PutVisible(VARIANT_TRUE);
            }
            
            // 获取活动文档
            activeDoc = wordApp->GetActiveDocument();
            if (!activeDoc) {
                cout << "没有活动的Word文档" << endl;
                return false;
            }
            
            return true;
        }
        catch (_com_error& e) {
            wcout << L"Word连接错误: " << e.ErrorMessage() << endl;
            return false;
        }
    }
    
    // 金额转换为万元格式
    bool Unit10000() {
        try {
            if (!wordApp || !activeDoc) {
                cout << "Word未初始化" << endl;
                return false;
            }
            
            Word::SelectionPtr selection = wordApp->GetSelection();
            _bstr_t selectedText = selection->GetText();
            
            string text = (char*)selectedText;
            if (text.empty()) {
                cout << "请选择要转换的数字" << endl;
                return false;
            }
            
            // 检查是否为数字
            if (IsNumeric(text)) {
                double number = stod(text);
                string formatted = FormatNumber(number, 2) + "万";
                
                selection->PutText(_bstr_t(formatted.c_str()));
                cout << "转换完成: " << text << " → " << formatted << endl;
                return true;
            } else {
                cout << "选中的内容不是有效数字: " << text << endl;
                return false;
            }
        }
        catch (_com_error& e) {
            wcout << L"金额转换失败: " << e.ErrorMessage() << endl;
            return false;
        }
    }
    
    // 表格求和
    bool GetSum() {
        try {
            if (!wordApp || !activeDoc) {
                cout << "Word未初始化" << endl;
                return false;
            }
            
            Word::SelectionPtr selection = wordApp->GetSelection();
            Word::CellsPtr cells = selection->GetCells();
            
            if (cells->GetCount() == 0) {
                cout << "请选择表格单元格" << endl;
                return false;
            }
            
            double total = 0.0;
            int cellCount = 0;
            
            for (int i = 1; i <= cells->GetCount(); i++) {
                Word::CellPtr cell = cells->Item(i);
                Word::RangePtr cellRange = cell->GetRange();
                _bstr_t cellText = cellRange->GetText();
                
                string text = (char*)cellText;
                text = TrimString(text);
                
                if (IsNumeric(text)) {
                    total += stod(text);
                    cellCount++;
                }
            }
            
            if (cellCount == 0) {
                cout << "选中的单元格中没有找到数字" << endl;
                return false;
            }
            
            string result = "合计: " + FormatNumber(total, 2) + 
                           "\n共计算了 " + to_string(cellCount) + " 个数字";
            cout << result << endl;
            
            // 在Word状态栏显示结果
            wordApp->PutStatusBar(_bstr_t(("合计: " + FormatNumber(total, 2)).c_str()));
            
            return true;
        }
        catch (_com_error& e) {
            wcout << L"表格求和失败: " << e.ErrorMessage() << endl;
            return false;
        }
    }
    
    // 添加千分位符
    bool AddThousandsSeparator() {
        try {
            if (!wordApp || !activeDoc) {
                cout << "Word未初始化" << endl;
                return false;
            }
            
            Word::SelectionPtr selection = wordApp->GetSelection();
            _bstr_t selectedText = selection->GetText();
            
            string text = (char*)selectedText;
            if (text.empty()) {
                cout << "请选择要处理的数字" << endl;
                return false;
            }
            
            // 使用正则表达式查找所有数字
            regex numberPattern(R"(\d+(?:\.\d+)?)");
            string result = text;
            
            // 从后往前替换，避免位置偏移
            vector<pair<size_t, size_t>> matches;
            sregex_iterator iter(text.begin(), text.end(), numberPattern);
            sregex_iterator end;
            
            for (; iter != end; ++iter) {
                matches.push_back({iter->position(), iter->length()});
            }
            
            // 从后往前处理
            for (auto it = matches.rbegin(); it != matches.rend(); ++it) {
                string numberStr = text.substr(it->first, it->second);
                if (IsNumeric(numberStr)) {
                    double number = stod(numberStr);
                    string formatted = FormatNumber(number, 2);
                    result.replace(it->first, it->second, formatted);
                }
            }
            
            selection->PutText(_bstr_t(result.c_str()));
            cout << "千分位符添加完成" << endl;
            return true;
        }
        catch (_com_error& e) {
            wcout << L"千分位符添加失败: " << e.ErrorMessage() << endl;
            return false;
        }
    }
    
    // 格式化表格
    bool FormatTable() {
        try {
            if (!wordApp || !activeDoc) {
                cout << "Word未初始化" << endl;
                return false;
            }
            
            Word::SelectionPtr selection = wordApp->GetSelection();
            
            // 检查是否在表格中
            if (!selection->GetInformation(Word::wdWithInTable)) {
                cout << "请将光标放在表格中" << endl;
                return false;
            }
            
            Word::TablePtr table = selection->GetTables()->Item(1);
            
            // 设置表格字体
            Word::RangePtr tableRange = table->GetRange();
            Word::FontPtr font = tableRange->GetFont();
            font->PutNameFarEast(_bstr_t("宋体"));
            font->PutNameAscii(_bstr_t("Times New Roman"));
            font->PutSize(11);
            
            // 设置表格边框
            Word::BordersPtr borders = table->GetBorders();
            borders->Item(Word::wdBorderTop)->PutLineStyle(Word::wdLineStyleDouble);
            borders->Item(Word::wdBorderBottom)->PutLineStyle(Word::wdLineStyleDouble);
            borders->Item(Word::wdBorderLeft)->PutLineStyle(Word::wdLineStyleNone);
            borders->Item(Word::wdBorderRight)->PutLineStyle(Word::wdLineStyleNone);
            borders->Item(Word::wdBorderHorizontal)->PutLineStyle(Word::wdLineStyleSingle);
            borders->Item(Word::wdBorderVertical)->PutLineStyle(Word::wdLineStyleSingle);
            
            // 自适应窗口
            table->AutoFitBehavior(Word::wdAutoFitWindow);
            
            cout << "表格格式化完成" << endl;
            return true;
        }
        catch (_com_error& e) {
            wcout << L"表格格式化失败: " << e.ErrorMessage() << endl;
            return false;
        }
    }
    
    // 去除空白
    bool RemoveBlankSpaces() {
        try {
            if (!wordApp || !activeDoc) {
                cout << "Word未初始化" << endl;
                return false;
            }
            
            Word::SelectionPtr selection = wordApp->GetSelection();
            _bstr_t selectedText = selection->GetText();
            
            string text = (char*)selectedText;
            if (text.empty()) {
                cout << "请选择要处理的文本" << endl;
                return false;
            }
            
            // 去除各种空格和多余换行
            string result = text;
            
            // 去除普通空格
            result = regex_replace(result, regex(" "), "");
            // 去除全角空格
            result = regex_replace(result, regex("　"), "");
            // 去除制表符
            result = regex_replace(result, regex("\t"), "");
            // 去除多余换行
            result = regex_replace(result, regex("\r\n\r\n"), "\r\n");
            result = regex_replace(result, regex("\n\n"), "\n");
            
            selection->PutText(_bstr_t(result.c_str()));
            cout << "空白清理完成" << endl;
            return true;
        }
        catch (_com_error& e) {
            wcout << L"空白清理失败: " << e.ErrorMessage() << endl;
            return false;
        }
    }

private:
    // 检查字符串是否为数字
    bool IsNumeric(const string& str) {
        if (str.empty()) return false;
        
        regex numberPattern(R"(^-?\d+(\.\d+)?$)");
        return regex_match(str, numberPattern);
    }
    
    // 格式化数字（添加千分位符）
    string FormatNumber(double number, int precision = 2) {
        stringstream ss;
        ss << fixed << setprecision(precision) << number;
        string str = ss.str();
        
        // 找到小数点位置
        size_t decimalPos = str.find('.');
        if (decimalPos == string::npos) {
            decimalPos = str.length();
        }
        
        // 从小数点前开始，每三位添加逗号
        for (int i = decimalPos - 3; i > 0; i -= 3) {
            str.insert(i, ",");
        }
        
        return str;
    }
    
    // 去除字符串首尾空格
    string TrimString(const string& str) {
        size_t start = str.find_first_not_of(" \t\r\n");
        if (start == string::npos) return "";
        
        size_t end = str.find_last_not_of(" \t\r\n");
        return str.substr(start, end - start + 1);
    }
};

// 主程序
int main() {
    // 设置控制台编码为UTF-8
    SetConsoleOutputCP(CP_UTF8);
    
    cout << "========================================" << endl;
    cout << "Word文档处理工具 - C++版本" << endl;
    cout << "========================================" << endl;
    
    WordProcessor processor;
    
    if (!processor.InitializeWord()) {
        cout << "Word初始化失败，请确保Word已启动并打开文档" << endl;
        system("pause");
        return 1;
    }
    
    cout << "Word连接成功！" << endl;
    
    int choice;
    do {
        cout << "\n请选择功能：" << endl;
        cout << "1. 金额转换为万元" << endl;
        cout << "2. 表格求和" << endl;
        cout << "3. 添加千分位符" << endl;
        cout << "4. 格式化表格" << endl;
        cout << "5. 去除空白" << endl;
        cout << "0. 退出" << endl;
        cout << "请输入选择 (0-5): ";
        
        cin >> choice;
        
        switch (choice) {
            case 1:
                processor.Unit10000();
                break;
            case 2:
                processor.GetSum();
                break;
            case 3:
                processor.AddThousandsSeparator();
                break;
            case 4:
                processor.FormatTable();
                break;
            case 5:
                processor.RemoveBlankSpaces();
                break;
            case 0:
                cout << "程序退出" << endl;
                break;
            default:
                cout << "无效选择，请重新输入" << endl;
                break;
        }
    } while (choice != 0);
    
    CoUninitialize();
    return 0;
}