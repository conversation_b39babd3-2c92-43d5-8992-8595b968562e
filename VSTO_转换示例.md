# VBA 转 VSTO 详细流程和示例

## 您的 VBA 代码转换分析

### 现有功能模块：
1. **SongtiTNR.vba** - 字体格式化和编号处理
2. **Table_AutoWidth.bas** - 表格智能调整
3. **tuomin.vba** - 文字脱敏处理

## 转换流程

### 第一步：环境搭建
1. 安装 Visual Studio 2019/2022
2. 安装 Office Developer Tools
3. 确保 .NET Framework 4.7.2+

### 第二步：项目创建
```
文件 → 新建 → 项目 → Visual C# → Office/SharePoint → 
选择 "Word VSTO Add-in" 或 "Word Document-level Customization"
```

### 第三步：核心转换示例

#### 1. VBA InputBox 转换为 Windows Forms
```csharp
// 替换 VBA 的 InputBox 和 MsgBox
public partial class FontSettingsForm : Form
{
    public string NumberFontName { get; set; }
    public double NumberFontSize { get; set; }
    public bool NumberIsBold { get; set; }
    public string TextFontName { get; set; }
    public double TextFontSize { get; set; }
    public bool TextIsBold { get; set; }
}
```

#### 2. 字体处理功能转换
```csharp
public class FontProcessor
{
    public void ProcessDocumentFonts(FontSettings settings)
    {
        try
        {
            var app = Globals.ThisAddIn.Application;
            app.ScreenUpdating = false;
            
            var selection = app.Selection;
            
            // 处理正文字体
            ProcessTextFont(selection, settings);
            
            // 处理编号字体
            ProcessNumberingFont(selection, settings);
            
            // 设置数字为 Times New Roman
            SetNumbersToTimesNewRoman(selection.Range);
            
            app.ScreenUpdating = true;
            MessageBox.Show("字体格式设置完成！", "完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"处理过程中出现错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
}
```

#### 3. 正则表达式处理转换
```csharp
private void SetNumbersToTimesNewRoman(Word.Range range)
{
    // VBA 使用 Word.Find 的通配符
    // VSTO 可以使用 .NET 正则表达式或继续使用 Word.Find
    
    var find = range.Find;
    find.ClearFormatting();
    find.Replacement.ClearFormatting();
    
    find.Text = "[0-9.,]+";
    find.Replacement.Text = "^&";
    find.Replacement.Font.Name = "Times New Roman";
    find.Replacement.Font.NameAscii = "Times New Roman";
    find.MatchWildcards = true;
    find.Forward = true;
    find.Wrap = Word.WdFindWrap.wdFindStop;
    find.Format = true;
    
    find.Execute(Replace: Word.WdReplace.wdReplaceAll);
}
```

### 第四步：Ribbon 界面设计
```xml
<!-- Ribbon.xml -->
<customUI xmlns="http://schemas.microsoft.com/office/2009/07/customui">
  <ribbon>
    <tabs>
      <tab id="CustomTab" label="文档处理">
        <group id="FontGroup" label="字体处理">
          <button id="SongtiTNRButton" 
                  label="字体格式化" 
                  size="large"
                  onAction="SongtiTNR_Click" />
          <button id="TableFormatButton" 
                  label="表格调整" 
                  size="large"
                  onAction="TableFormat_Click" />
          <button id="DesensitizeButton" 
                  label="文字脱敏" 
                  size="large"
                  onAction="Desensitize_Click" />
        </group>
      </tab>
    </tabs>
  </ribbon>
</customUI>
```

### 第五步：部署配置
1. **ClickOnce 部署**：适合内部分发
2. **Windows Installer**：适合正式发布
3. **Office Store**：适合公开发布

## 转换优势

### VSTO 相比 VBA 的优势：
1. **更强的类型安全**：编译时错误检查
2. **更好的调试支持**：Visual Studio 调试器
3. **更丰富的 .NET 库**：正则表达式、文件处理、网络访问
4. **更好的部署**：ClickOnce、Windows Installer
5. **更好的维护性**：面向对象设计、单元测试
6. **更好的性能**：编译代码 vs 解释执行

### 注意事项：
1. **学习曲线**：需要掌握 C# 和 .NET
2. **部署复杂性**：比 VBA 宏复杂
3. **兼容性**：需要考虑不同 Office 版本
4. **安全策略**：企业环境可能需要额外配置

## 建议的转换顺序：
1. 先转换最简单的功能（如字体设置）
2. 再转换中等复杂度功能（如表格处理）
3. 最后转换复杂功能（如脱敏处理）
4. 逐步测试和优化每个模块