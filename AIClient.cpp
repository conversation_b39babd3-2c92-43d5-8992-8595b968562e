// 注意：本代码为伪代码风格的C++实现，需要实际使用COM接口或相关库实现
#include <iostream>
#include <string>
#include <vector>
#include <map>
#include <windows.h>
#include <comdef.h>
#include <comutil.h>
#include <sstream> // 补全缺少的头文件

// 关键修复：导入Word类型库。
        "https://api.anthropic.com/v1/messages", 
        "", "claude-3-sonnet-20240229"));
    
    apiConfigs.push_back(APIConfig("通义千问", 
        "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation", 
        "", "qwen-turbo"));
    
    LoadAPISettings();
}

AIClient::~AIClient() {
    // 析构函数
}

void AIClient::LoadAPISettings() {
    for (auto& config : apiConfigs) {
        std::string keyName = config.name + "_Key";
        config.key = GetRegistryValue(keyName);
        config.enabled = !config.key.empty();
    }
}

void AIClient::SaveAPISettings() {
    for (const auto& config : apiConfigs) {
        std::string keyName = config.name + "_Key";
        SetRegistryValue(keyName, config.key);
    }
}

void AIClient::ConfigureAPI() {
    std::string menu = "AI API 配置\n";
    menu += "==============================\n\n";
    menu += "当前配置状态：\n";
    
    for (size_t i = 0; i < apiConfigs.size(); ++i) {
        menu += std::to_string(i + 1) + ". " + apiConfigs[i].name + ": ";
        menu += apiConfigs[i].enabled ? "已配置\n" : "未配置\n";
    }
    
    menu += "\n请选择要配置的API (1-" + std::to_string(apiConfigs.size()) + "):";
    
    std::string choice = InputDialog(menu, "API配置", "1");
    
    if (!choice.empty()) {
        int index = std::stoi(choice) - 1;
        if (index >= 0 && index < apiConfigs.size()) {
            std::string prompt = "请输入 " + apiConfigs[index].name + " API Key:\n\n";
            
            if (apiConfigs[index].name == "OpenAI") {
                prompt += "获取地址: https://platform.openai.com/api-keys\n";
                prompt += "格式: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";
            } else if (apiConfigs[index].name == "Claude") {
                prompt += "获取地址: https://console.anthropic.com/\n";
                prompt += "格式: sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";
            } else if (apiConfigs[index].name == "通义千问") {
                prompt += "获取地址: https://dashscope.console.aliyun.com/\n";
                prompt += "格式: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx";
            }
            
            std::string newKey = InputDialog(prompt, apiConfigs[index].name + " 配置", 
                                           apiConfigs[index].key);
            
            if (!newKey.empty()) {
                apiConfigs[index].key = newKey;
                apiConfigs[index].enabled = true;
                SaveAPISettings();
                MessageDialog(apiConfigs[index].name + " API Key 配置成功！", 
                            "配置完成", MB_OK | MB_ICONINFORMATION);
            }
        }
    }
}

std::vector<AIClient::AIResponse> AIClient::CallAI(const std::string& prompt) {
    std::vector<AIResponse> responses;
    
    for (const auto& config : apiConfigs) {
        if (config.enabled) {
            AIResponse response = CallSingleAPI(config, prompt);
            responses.push_back(response);
        }
    }
    
    return responses;
}

AIClient::AIResponse AIClient::CallSingleAPI(const APIConfig& config, const std::string& prompt) {
    AIResponse response;
    response.apiName = config.name;
    
    // 添加重试机制
    const int maxRetries = 3;
    const int retryDelayBase = 35; // 基础延迟时间(秒)
    
    for (int attempt = 0; attempt <= maxRetries; ++attempt) {
        try {
            std::string jsonBody;
            std::string headers = "Content-Type: application/json\r\n";
            headers += "Authorization: Bearer " + config.key + "\r\n";
            
            // 构建不同API的请求体
            if (config.name == "OpenAI") {
                jsonBody = R"({"model":")" + config.model + R"(","messages":[{"role":"user","content":")" + 
                          EscapeJSON(prompt) + R"("}],"max_tokens":1000,"temperature":0.7})";
            } 
            else if (config.name == "Claude") {
                headers += "anthropic-version: 2023-06-01\r\n";
                jsonBody = R"({"model":")" + config.model + R"(","max_tokens":1000,"messages":[{"role":"user","content":")" + 
                          EscapeJSON(prompt) + R"("}]})";
            }
            else if (config.name == "通义千问") {
                headers += "X-DashScope-SSE: disable\r\n";
                jsonBody = R"({"model":")" + config.model + R"(","input":{"messages":[{"role":"user","content":")" + 
                          EscapeJSON(prompt) + R"("}]},"parameters":{"max_tokens":1000,"temperature":0.7}})";
            }
            
            std::string responseText = SendHTTPRequest(config.url, headers, jsonBody, response.statusCode);
            
            if (response.statusCode == 200) {
                // 解析响应
                std::string fieldName = (config.name == "Claude") ? "text" : "content";
                response.content = ExtractJSONField(responseText, fieldName);
                response.content = UnescapeJSON(response.content);
                response.success = !response.content.empty();
                break; // 成功，跳出重试循环
            } else if (response.statusCode == 429 && attempt < maxRetries) {
                // 遇到429错误且还有重试次数，等待后重试
                int delaySeconds = retryDelayBase * (attempt + 1); // 逐步增加延迟时间
                std::string retryMessage = "API调用遇到配额限制(429错误)，" + std::to_string(delaySeconds) + "秒后进行第" + 
                                          std::to_string(attempt + 1) + "次重试...";
                MessageDialog(retryMessage, "API配额限制", MB_OK | MB_ICONWARNING);
                
                // 等待指定秒数
                Sleep(delaySeconds * 1000);
                continue; // 继续下一次尝试
            } else {
                response.error = "HTTP错误: " + std::to_string(response.statusCode);
                break; // 其他错误，跳出重试循环
            }
        }
        catch (const std::exception& e) {
            response.error = "异常: " + std::string(e.what());
            break; // 异常，跳出重试循环
        }
    }
    
    return response;
}

std::string AIClient::SendHTTPRequest(const std::string& url, const std::string& headers, 
                                     const std::string& data, int& statusCode) {
    std::string response;
    statusCode = 0;
    
    HINTERNET hInternet = InternetOpenA("AIClient/1.0", INTERNET_OPEN_TYPE_DIRECT, NULL, NULL, 0);
    if (!hInternet) return response;
    
    // 解析URL
    std::string hostname, path;
    size_t pos = url.find("://");
    if (pos != std::string::npos) {
        pos += 3;
        size_t pathPos = url.find("/", pos);
        if (pathPos != std::string::npos) {
            hostname = url.substr(pos, pathPos - pos);
            path = url.substr(pathPos);
        } else {
            hostname = url.substr(pos);
            path = "/";
        }
    }
    
    HINTERNET hConnect = InternetConnectA(hInternet, hostname.c_str(), INTERNET_DEFAULT_HTTPS_PORT,
                                         NULL, NULL, INTERNET_SERVICE_HTTP, 0, 0);
    if (!hConnect) {
        InternetCloseHandle(hInternet);
        return response;
    }
    
    HINTERNET hRequest = HttpOpenRequestA(hConnect, "POST", path.c_str(), NULL, NULL, NULL,
                                         INTERNET_FLAG_SECURE | INTERNET_FLAG_RELOAD, 0);
    if (!hRequest) {
        InternetCloseHandle(hConnect);
        InternetCloseHandle(hInternet);
        return response;
    }
    
    // 发送请求
    BOOL result = HttpSendRequestA(hRequest, headers.c_str(), headers.length(),
                                  (LPVOID)data.c_str(), data.length());
    
    if (result) {
        // 获取状态码
        DWORD statusCodeSize = sizeof(DWORD);
        HttpQueryInfoA(hRequest, HTTP_QUERY_STATUS_CODE | HTTP_QUERY_FLAG_NUMBER,
                      &statusCode, &statusCodeSize, NULL);
        
        // 读取响应
        char buffer[4096];
        DWORD bytesRead;
        while (InternetReadFile(hRequest, buffer, sizeof(buffer), &bytesRead) && bytesRead > 0) {
            response.append(buffer, bytesRead);
        }
    }
    
    InternetCloseHandle(hRequest);
    InternetCloseHandle(hConnect);
    InternetCloseHandle(hInternet);
    
    return response;
}

std::string AIClient::EscapeJSON(const std::string& text) {
    std::string result = text;
    
    // 替换特殊字符
    size_t pos = 0;
    while ((pos = result.find("\\", pos)) != std::string::npos) {
        result.replace(pos, 1, "\\\\");
        pos += 2;
    }
    
    pos = 0;
    while ((pos = result.find("\"", pos)) != std::string::npos) {
        result.replace(pos, 1, "\\\"");
        pos += 2;
    }
    
    pos = 0;
    while ((pos = result.find("\n", pos)) != std::string::npos) {
        result.replace(pos, 1, "\\n");
        pos += 2;
    }
    
    pos = 0;
    while ((pos = result.find("\r", pos)) != std::string::npos) {
        result.replace(pos, 1, "\\r");
        pos += 2;
    }
    
    return result;
}

std::string AIClient::UnescapeJSON(const std::string& text) {
    std::string result = text;
    
    size_t pos = 0;
    while ((pos = result.find("\\n", pos)) != std::string::npos) {
        result.replace(pos, 2, "\n");
        pos += 1;
    }
    
    pos = 0;
    while ((pos = result.find("\\r", pos)) != std::string::npos) {
        result.replace(pos, 2, "\r");
        pos += 1;
    }
    
    pos = 0;
    while ((pos = result.find("\\\"", pos)) != std::string::npos) {
        result.replace(pos, 2, "\"");
        pos += 1;
    }
    
    pos = 0;
    while ((pos = result.find("\\\\", pos)) != std::string::npos) {
        result.replace(pos, 2, "\\");
        pos += 1;
    }
    
    return result;
}

std::string AIClient::ExtractJSONField(const std::string& json, const std::string& field) {
    std::string searchStr = "\"" + field + "\":\"";
    size_t startPos = json.find(searchStr);
    
    if (startPos != std::string::npos) {
        startPos += searchStr.length();
        size_t endPos = json.find("\"", startPos);
        
        // 处理转义字符
        while (endPos != std::string::npos && endPos > 0 && json[endPos - 1] == '\\') {
            endPos = json.find("\"", endPos + 1);
        }
        
        if (endPos != std::string::npos) {
            return json.substr(startPos, endPos - startPos);
        }
    }
    
    return "";
}

std::string AIClient::GetRegistryValue(const std::string& key, const std::string& defaultValue) {
    HKEY hKey;
    LONG result = RegOpenKeyExA(HKEY_CURRENT_USER, registryPath.c_str(), 0, KEY_READ, &hKey);
    
    if (result != ERROR_SUCCESS) {
        return defaultValue;
    }
    
    char buffer[1024];
    DWORD bufferSize = sizeof(buffer);
    DWORD type;
    
    result = RegQueryValueExA(hKey, key.c_str(), NULL, &type, (LPBYTE)buffer, &bufferSize);
    RegCloseKey(hKey);
    
    if (result == ERROR_SUCCESS && type == REG_SZ) {
        return std::string(buffer);
    }
    
    return defaultValue;
}

void AIClient::SetRegistryValue(const std::string& key, const std::string& value) {
    HKEY hKey;
    LONG result = RegCreateKeyExA(HKEY_CURRENT_USER, registryPath.c_str(), 0, NULL,
                                 REG_OPTION_NON_VOLATILE, KEY_WRITE, NULL, &hKey, NULL);
    
    if (result == ERROR_SUCCESS) {
        RegSetValueExA(hKey, key.c_str(), 0, REG_SZ, (const BYTE*)value.c_str(), value.length() + 1);
        RegCloseKey(hKey);
    }
}

void AIClient::DeleteRegistryValue(const std::string& key) {
    HKEY hKey;
    LONG result = RegOpenKeyExA(HKEY_CURRENT_USER, registryPath.c_str(), 0, KEY_WRITE, &hKey);
    
    if (result == ERROR_SUCCESS) {
        RegDeleteValueA(hKey, key.c_str());
        RegCloseKey(hKey);
    }
}

std::string AIClient::InputDialog(const std::string& prompt, const std::string& title, 
                                 const std::string& defaultValue) {
    // 简化版输入对话框 - 实际项目中可以使用更复杂的UI库
    std::cout << title << std::endl;
    std::cout << prompt << std::endl;
    if (!defaultValue.empty()) {
        std::cout << "默认值: " << defaultValue << std::endl;
    }
    std::cout << "请输入: ";
    
    std::string input;
    std::getline(std::cin, input);
    
    return input.empty() ? defaultValue : input;
}

int AIClient::MessageDialog(const std::string& message, const std::string& title, UINT type) {
    return MessageBoxA(NULL, message.c_str(), title.c_str(), type);
}

void AIClient::ShowResultDialog(const std::vector<AIResponse>& responses) {
    std::string result = "AI调用结果:\n";
    result += "==============================\n\n";
    
    for (const auto& response : responses) {
        result += "【" + response.apiName + "】\n";
        if (response.success) {
            result += response.content + "\n\n";
        } else {
            result += "调用失败: " + response.error + "\n\n";
        }
    }
    
    MessageDialog(result, "AI调用结果", MB_OK | MB_ICONINFORMATION);
}

void AIClient::ClearAllSettings() {
    if (MessageDialog("确定要清除所有API配置吗？", "确认清除", 
                     MB_YESNO | MB_ICONQUESTION) == IDYES) {
        for (auto& config : apiConfigs) {
            std::string keyName = config.name + "_Key";
            DeleteRegistryValue(keyName);
            config.key = "";
            config.enabled = false;
        }
        MessageDialog("所有API配置已清除", "清除完成", MB_OK | MB_ICONINFORMATION);
    }
}