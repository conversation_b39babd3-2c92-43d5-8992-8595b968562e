cmake_minimum_required(VERSION 3.16)
project(WordProcessor)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译器选项
if(MSVC)
    # Visual Studio编译器选项
    add_compile_options(/utf-8)
    add_compile_definitions(_WIN32_DCOM)
    add_compile_definitions(UNICODE _UNICODE)
endif()

# 查找必要的库
find_package(Threads REQUIRED)

# 添加可执行文件
add_executable(WordProcessor NumberProcessor.cpp)

# 链接必要的库
target_link_libraries(WordProcessor 
    ole32
    oleaut32
    uuid
    Threads::Threads
)

# 设置输出目录
set_target_properties(WordProcessor PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)