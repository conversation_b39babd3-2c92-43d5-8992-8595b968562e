@echo off
echo ========================================
echo Word文档处理工具 - C++版本编译脚本
echo ========================================
echo.

echo 正在查找Visual Studio编译器...

REM 查找Visual Studio 2022
set "VCVARS_PATH="
if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat" (
    set "VCVARS_PATH=%ProgramFiles%\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat"
    echo 找到Visual Studio 2022 Enterprise
) else if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    set "VCVARS_PATH=%ProgramFiles%\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
    echo 找到Visual Studio 2022 Professional
) else if exist "%ProgramFiles%\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    set "VCVARS_PATH=%ProgramFiles%\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    echo 找到Visual Studio 2022 Community
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Enterprise\VC\Auxiliary\Build\vcvars64.bat" (
    set "VCVARS_PATH=%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Enterprise\VC\Auxiliary\Build\vcvars64.bat"
    echo 找到Visual Studio 2019 Enterprise
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    set "VCVARS_PATH=%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat"
    echo 找到Visual Studio 2019 Professional
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    set "VCVARS_PATH=%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    echo 找到Visual Studio 2019 Community
) else (
    echo 未找到Visual Studio编译器
    echo 请安装Visual Studio 2019或2022
    pause
    exit /b 1
)

echo.
echo 正在设置编译环境...
call "%VCVARS_PATH%"

echo.
echo 正在编译C++程序...
cl /EHsc /std:c++17 /DUNICODE /D_UNICODE /D_WIN32_DCOM NumberProcessor.cpp ole32.lib oleaut32.lib uuid.lib /Fe:WordProcessor.exe

if errorlevel 1 (
    echo.
    echo 编译失败
    pause
    exit /b 1
) else (
    echo.
    echo 编译成功！
    echo 可执行文件: WordProcessor.exe
    echo.
    echo 使用说明：
    echo 1. 确保Microsoft Word已安装
    echo 2. 先在Word中打开要处理的文档
    echo 3. 运行WordProcessor.exe
    echo 4. 根据菜单选择相应功能
    echo.
    echo 功能列表：
    echo - 金额转换为万元：将选中数字转换为万元格式
    echo - 表格求和：计算选中表格单元格的数值总和
    echo - 添加千分位符：为数字添加千分位逗号分隔符
    echo - 格式化表格：美化Word表格的格式和边框
    echo - 去除空白：清理文档中的多余空格和换行
)

echo.
pause