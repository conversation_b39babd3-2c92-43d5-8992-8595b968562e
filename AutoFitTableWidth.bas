Sub AutoFitTableToPageWidth()
    '让Word表格列宽按内容适应，再等比放大至整页
    
    Dim tbl As Table
    Dim curTotal As Single
    Dim newTotal As Single
    Dim scale As Single
    Dim i As Long
    
    ' 检查是否在表格中
    If Selection.Information(wdWithInTable) Then
        Set tbl = Selection.Tables(1)
    Else
        MsgBox "请将光标放在表格中再运行此宏。"
        Exit Sub
    End If
    
    Application.ScreenUpdating = False
    
    '① 让 Word 按内容计算最小列宽
    tbl.AutoFitBehavior wdAutoFitContent
    
    '② 统计当前总宽（单位：磅）
    curTotal = 0
    For i = 1 To tbl.Columns.Count
        curTotal = curTotal + tbl.Columns(i).Width
    Next i
    
    '③ 页面可用宽度 = 页面宽度 - 左右页边距
    With tbl.Range.Document.PageSetup  ' 修正：正确的文档引用方式
        newTotal = .PageWidth - .LeftMargin - .RightMargin
    End With
    
    '④ 等比放大各列至整页
    If curTotal > 0 Then
        scale = newTotal / curTotal
        For i = 1 To tbl.Columns.Count
            With tbl.Columns(i)
                .PreferredWidthType = wdPreferredWidthPoints
                .PreferredWidth = .Width * scale
            End With
        Next i
    End If
    
    '⑤ 锁定列宽，禁止后续 AutoFit
    tbl.AllowAutoFit = False
    
    Application.ScreenUpdating = True
    MsgBox "表格已按内容自适应并等比放大至页面宽度！"
End Sub

'-----------------------------------------------------------
' 集成到FormatSingleTable中的版本
Sub FormatSingleTableWithAutoFit(control As IRibbonControl)
    '单表-一键美颜（集成智能自适应列宽）
    Application.ScreenUpdating = False
    Application.DisplayAlerts = False
    On Error Resume Next
    
    Dim myTable As Table
    Dim i As Long, j As Long
    Dim cellText As String
    Dim hasPercentColumn() As Boolean
    
    ' 检查是否在表格中
    If Selection.Information(wdWithInTable) Then
        Set myTable = Selection.Tables(1)
    Else
        MsgBox "请将光标放在表格中或选中表格后再运行此宏。"
        Exit Sub
    End If
    
    ' 初始化百分号列数组
    ReDim hasPercentColumn(1 To myTable.Columns.Count)
    
    ' === 第一部分：设置基础格式 ===
    With myTable
        ' 单元格边距
        .TopPadding = PixelsToPoints(2, True)
        .BottomPadding = PixelsToPoints(2, True)
        .LeftPadding = PixelsToPoints(7, True)
        .RightPadding = PixelsToPoints(10, True)
        
        ' 字体格式
        With .Range.Font
            .NameFarEast = "宋体"
            .NameAscii = "Times New Roman"
            .Size = 11
            .Spacing = 0
            .Scaling = 100
        End With
        
        ' 行高设置
        For i = 1 To .Rows.Count
            .Rows(i).HeightRule = wdRowHeightAtLeast
            .Rows(i).Height = CentimetersToPoints(0.7)
            Err.Clear
        Next i
    End With
    
    ' === 第二部分：识别包含百分号的列 ===
    With myTable
        For j = 1 To .Columns.Count
            On Error Resume Next
            cellText = Left(.Cell(1, j).Range.Text, Len(.Cell(1, j).Range.Text) - 2)
            If InStr(cellText, "%") > 0 Then
                hasPercentColumn(j) = True
            End If
            Err.Clear
        Next j
    End With
    
    ' === 第三部分：统一设置单元格格式和对齐 ===
    With myTable
        For i = 1 To .Rows.Count
            For j = 1 To .Columns.Count
                If Err.Number = 0 Then
                    cellText = Left(.Cell(i, j).Range.Text, Len(.Cell(i, j).Range.Text) - 2)
                    
                    ' 设置基础段落格式
                    With .Cell(i, j).Range.ParagraphFormat
                        .SpaceBefore = 0: .SpaceAfter = 0
                        .FirstLineIndent = 0: .LeftIndent = 0: .RightIndent = 0
                        .LineSpacingRule = wdLineSpaceExactly: .LineSpacing = 16
                        .AutoAdjustRightIndent = False
                    End With
                    
                    ' 设置垂直居中
                    .Cell(i, j).VerticalAlignment = wdCellAlignVerticalCenter
                    
                    ' 根据位置和内容设置对齐方式
                    If i = 1 Then
                        ' 第一行：居中加粗
                        .Cell(i, j).Range.Font.Bold = True
                        .Cell(i, j).Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                    ElseIf j = 1 And i = .Rows.Count Then
                        ' 第一列最后一个单元格：居中对齐
                        .Cell(i, j).Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                    ElseIf j = 1 Then
                        ' 第一列其他行：左对齐
                        .Cell(i, j).Range.ParagraphFormat.Alignment = wdAlignParagraphLeft
                    ElseIf InStr(cellText, "%") > 0 Then
                        ' 包含百分号的单元格：居中对齐
                        .Cell(i, j).Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                    ElseIf hasPercentColumn(j) And IsNumeric(cellText) And Len(Trim(cellText)) > 0 Then
                        ' 百分号列下的数字：居中对齐
                        .Cell(i, j).Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                        .Cell(i, j).WordWrap = False
                    ElseIf IsNumeric(cellText) And Len(Trim(cellText)) > 0 Then
                        ' 其他列的数字：右对齐
                        .Cell(i, j).Range.ParagraphFormat.Alignment = wdAlignParagraphRight
                        .Cell(i, j).WordWrap = False
                    Else
                        ' 其他：居中对齐
                        .Cell(i, j).Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                    End If
                    
                    ' 最后一行加粗
                    If i = .Rows.Count Then
                        .Cell(i, j).Range.Font.Bold = True
                    End If
                End If
                Err.Clear
            Next j
        Next i
    End With
    
    ' === 第四部分：智能自适应列宽调整 ===
    Call ApplySmartAutoFit(myTable)
    
    ' === 第五部分：设置边框 ===
    With myTable
        .Borders(wdBorderLeft).LineStyle = wdLineStyleNone
        .Borders(wdBorderRight).LineStyle = wdLineStyleNone
        
        With .Borders(wdBorderTop)
            .LineStyle = wdLineStyleDouble
            .LineWidth = wdLineWidth050pt
        End With
        
        With .Borders(wdBorderBottom)
            .LineStyle = wdLineStyleDouble
            .LineWidth = wdLineWidth050pt
        End With
        
        With .Borders(wdBorderHorizontal)
            .LineStyle = wdLineStyleSingle
            .LineWidth = wdLineWidth050pt
        End With
        
        With .Borders(wdBorderVertical)
            .LineStyle = wdLineStyleSingle
            .LineWidth = wdLineWidth050pt
        End With
    End With
    
    ' === 第六部分：删除数字行下方的空行 ===
    With myTable
        For i = .Rows.Count To 2 Step -1
            Dim rowHasNumber As Boolean: rowHasNumber = False
            Dim rowBelowEmpty As Boolean: rowBelowEmpty = True
            
            ' 检查上一行是否有数字
            For j = 1 To .Columns.Count
                cellText = Replace(Replace(.Cell(i - 1, j).Range.Text, Chr(13), ""), Chr(7), "")
                If IsNumeric(cellText) And Len(Trim(cellText)) > 0 Then
                    rowHasNumber = True: Exit For
                End If
                Err.Clear
            Next j
            
            ' 如果上一行有数字，检查当前行是否为空
            If rowHasNumber Then
                For j = 1 To .Columns.Count
                    cellText = Replace(Replace(.Cell(i, j).Range.Text, Chr(13), ""), Chr(7), "")
                    If Len(Trim(cellText)) > 0 Then
                        rowBelowEmpty = False: Exit For
                    End If
                    Err.Clear
                Next j
                
                ' 删除空行
                If rowBelowEmpty Then .Rows(i).Delete
            End If
            Err.Clear
        Next i
    End With
    
    Application.DisplayAlerts = True
    Application.ScreenUpdating = True
    MsgBox "表格格式化完成！列宽已智能自适应调整。"
End Sub

'-----------------------------------------------------------
' 智能自适应列宽调整函数
Private Sub ApplySmartAutoFit(tbl As Table)
    On Error Resume Next
    
    Dim curTotal As Single
    Dim newTotal As Single
    Dim scale As Single
    Dim i As Long
    
    '① 让 Word 按内容计算最小列宽
    tbl.AutoFitBehavior wdAutoFitContent
    
    '② 统计当前总宽（单位：磅）
    curTotal = 0
    For i = 1 To tbl.Columns.Count
        curTotal = curTotal + tbl.Columns(i).Width
    Next i
    
    '③ 页面可用宽度 = 页面宽度 - 左右页边距
    With tbl.Range.Document.PageSetup
        newTotal = .PageWidth - .LeftMargin - .RightMargin
    End With
    
    '④ 等比放大各列至整页
    If curTotal > 0 Then
        scale = newTotal / curTotal
        For i = 1 To tbl.Columns.Count
            With tbl.Columns(i)
                .PreferredWidthType = wdPreferredWidthPoints
                .PreferredWidth = .Width * scale
            End With
        Next i
    End If
    
    '⑤ 锁定列宽，禁止后续 AutoFit
    tbl.AllowAutoFit = False
    
    Err.Clear
End Sub

'-----------------------------------------------------------
' 简化版本 - 只调整列宽
Sub QuickAutoFitToPage()
    '快速将表格列宽自适应到页面宽度
    
    Dim tbl As Table
    
    If Selection.Information(wdWithInTable) Then
        Set tbl = Selection.Tables(1)
    Else
        MsgBox "请将光标放在表格中。"
        Exit Sub
    End If
    
    Application.ScreenUpdating = False
    
    ' 应用智能自适应
    Call ApplySmartAutoFit(tbl)
    
    Application.ScreenUpdating = True
    MsgBox "表格列宽已自适应调整到页面宽度！"
End Sub

'-----------------------------------------------------------
' 恢复自动调整功能
Sub RestoreAutoFit()
    '恢复表格的自动调整功能
    
    Dim tbl As Table
    
    If Selection.Information(wdWithInTable) Then
        Set tbl = Selection.Tables(1)
    Else
        MsgBox "请将光标放在表格中。"
        Exit Sub
    End If
    
    ' 恢复自动调整
    tbl.AllowAutoFit = True
    tbl.AutoFitBehavior wdAutoFitWindow
    
    MsgBox "表格自动调整功能已恢复！"
End Sub