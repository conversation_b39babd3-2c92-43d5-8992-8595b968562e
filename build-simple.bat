@echo off
echo ========================================
echo Word文档处理工具 - 简化版编译脚本
echo ========================================
echo.

echo 正在编译简化版项目...
dotnet build WordProcessorSimple.vbproj -c Release

if errorlevel 1 (
    echo.
    echo 编译失败
    pause
    exit /b 1
)

echo.
echo 正在发布独立可执行文件...
dotnet publish WordProcessorSimple.vbproj -c Release -r win-x64 -p:PublishSingleFile=true -p:SelfContained=true -o publish-simple

if errorlevel 1 (
    echo.
    echo 发布失败
    pause
    exit /b 1
) else (
    echo.
    echo 发布成功！
    echo 文件位置: publish-simple\WordProcessorSimple.exe
)

echo.
echo 简化版功能说明：
echo - 测试Word连接：检查是否能连接到Word
echo - 金额万位到元：将选中数字转换为万元格式
echo - 表格求和：计算选中表格单元格的数值总和
echo.
echo 使用方法：
echo 1. 先打开Word并打开文档
echo 2. 运行此程序
echo 3. 点击"测试Word连接"确认连接正常
echo 4. 在Word中选择内容后使用相应功能
echo.
pause