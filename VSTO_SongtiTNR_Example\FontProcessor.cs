using System;
using System.Collections.Generic;
using System.Windows.Forms;
using Word = Microsoft.Office.Interop.Word;

namespace WordFontFormatterAddIn
{
    /// <summary>
    /// 字体处理器 - 核心业务逻辑，对应 VBA 的 SongtiTNR 功能
    /// </summary>
    public class FontProcessor
    {
        /// <summary>
        /// 主要的字体处理方法 - 对应 VBA 的 SongtiTNR Sub
        /// </summary>
        public void ProcessDocumentFonts()
        {
            try
            {
                // 显示设置对话框
                var settingsForm = new FontSettingsForm();
                if (settingsForm.ShowDialog() != DialogResult.OK)
                {
                    return; // 用户取消
                }

                var settings = settingsForm.Settings;
                
                // 确认对话框
                var confirmResult = MessageBox.Show(
                    $"即将应用以下设置：\n\n{settings.GetSummary()}\n\n确定继续吗？",
                    "确认设置",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (confirmResult != DialogResult.Yes)
                {
                    return;
                }

                var app = Globals.ThisAddIn.Application;
                app.ScreenUpdating = false;
                app.DisplayAlerts = Word.WdAlertLevel.wdAlertsNone;

                try
                {
                    var selection = app.Selection;
                    
                    // 备份原始选择范围
                    var originalSelection = selection.Range.Duplicate;

                    // 1. 修改正文字体格式
                    ProcessTextFormat(selection, settings);

                    // 2. 修改编号字体格式
                    ProcessNumberingFormat(selection, settings);

                    // 3. 将正文中的数字字体改为 Times New Roman
                    SetNumbersToTimesNewRoman(selection.Range);

                    // 刷新文档并恢复选择
                    app.ActiveDocument.Range().Fields.Update();
                    originalSelection.Select();

                    MessageBox.Show("编号和正文字体已修改完成！数字已设置为Times New Roman。", 
                                  "设置完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                finally
                {
                    app.ScreenUpdating = true;
                    app.DisplayAlerts = Word.WdAlertLevel.wdAlertsAll;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"处理过程中出现错误：{ex.Message}", "错误", 
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 处理正文字体格式 - 对应 VBA 中的正文字体处理部分
        /// </summary>
        private void ProcessTextFormat(Word.Selection selection, FontSettings settings)
        {
            if (string.IsNullOrEmpty(settings.TextFontName)) return;

            try
            {
                var font = selection.Font;
                font.Name = settings.TextFontName;
                font.NameFarEast = settings.TextFontName;
                
                if (settings.TextFontSize > 0)
                {
                    font.Size = (float)settings.TextFontSize;
                }
                
                font.Bold = settings.TextIsBold ? 1 : 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"处理正文字体时出错：{ex.Message}");
            }
        }

        /// <summary>
        /// 处理编号字体格式 - 对应 VBA 中的编号字体处理部分
        /// </summary>
        private void ProcessNumberingFormat(Word.Selection selection, FontSettings settings)
        {
            if (string.IsNullOrEmpty(settings.NumberFontName)) return;

            try
            {
                var processedTemplates = new Dictionary<string, bool>();

                foreach (Word.Paragraph para in selection.Paragraphs)
                {
                    if (para.Range.ListFormat.ListType != Word.WdListType.wdListNoNumbering)
                    {
                        var listTemplate = para.Range.ListFormat.ListTemplate;
                        var currentLevel = para.Range.ListFormat.ListLevelNumber;
                        var templateKey = $"{listTemplate.GetHashCode()}_{currentLevel}";

                        // 检查是否已处理过该模板
                        if (!processedTemplates.ContainsKey(templateKey))
                        {
                            if (listTemplate != null && currentLevel >= 1 && currentLevel <= listTemplate.ListLevels.Count)
                            {
                                var listLevel = listTemplate.ListLevels[currentLevel];
                                var font = listLevel.Font;
                                
                                font.Name = settings.NumberFontName;
                                font.NameFarEast = settings.NumberFontName;
                                
                                if (settings.NumberFontSize > 0)
                                {
                                    font.Size = (float)settings.NumberFontSize;
                                }
                                
                                font.Bold = settings.NumberIsBold ? 1 : 0;
                                
                                processedTemplates[templateKey] = true;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"处理编号字体时出错：{ex.Message}");
            }
        }

        /// <summary>
        /// 将数字设置为 Times New Roman - 对应 VBA 的 SetNumbersToTimesNewRoman
        /// </summary>
        private void SetNumbersToTimesNewRoman(Word.Range targetRange)
        {
            try
            {
                var find = targetRange.Find;
                find.ClearFormatting();
                find.Replacement.ClearFormatting();
                
                find.Text = "[0-9.,]+";
                find.Replacement.Text = "^&";
                find.Replacement.Font.Name = "Times New Roman";
                find.Replacement.Font.NameAscii = "Times New Roman";
                find.Forward = true;
                find.Wrap = Word.WdFindWrap.wdFindStop;
                find.Format = true;
                find.MatchCase = false;
                find.MatchWholeWord = false;
                find.MatchAllWordForms = false;
                find.MatchSoundsLike = false;
                find.MatchWildcards = true;

                // 执行查找替换直到没有更多匹配项
                while (find.Execute(Replace: Word.WdReplace.wdReplaceAll))
                {
                    // 继续查找直到没有更多匹配
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"设置数字字体时出错：{ex.Message}");
            }
        }
    }
}