#include <iostream>
#include <string>
#include <vector>
#include <regex>
#include <iomanip>
#include <sstream>
#include <fstream>
#include <windows.h>

using namespace std;

class SimpleNumberProcessor {
public:
    // 金额转换为万元格式
    string ConvertToWanYuan(const string& input) {
        regex numberPattern(R"(\d+(?:\.\d+)?)");
        string result = input;
        
        // 查找所有数字并替换
        sregex_iterator iter(input.begin(), input.end(), numberPattern);
        sregex_iterator end;
        
        vector<pair<size_t, size_t>> matches;
        for (; iter != end; ++iter) {
            matches.push_back({iter->position(), iter->length()});
        }
        
        // 从后往前替换，避免位置偏移
        for (auto it = matches.rbegin(); it != matches.rend(); ++it) {
            string numberStr = input.substr(it->first, it->second);
            if (IsNumeric(numberStr)) {
                double number = stod(numberStr);
                string formatted = FormatNumber(number, 2) + "万";
                result.replace(it->first, it->second, formatted);
            }
        }
        
        return result;
    }
    
    // 添加千分位符
    string AddThousandsSeparator(const string& input) {
        regex numberPattern(R"(\d+(?:\.\d+)?)");
        string result = input;
        
        sregex_iterator iter(input.begin(), input.end(), numberPattern);
        sregex_iterator end;
        
        vector<pair<size_t, size_t>> matches;
        for (; iter != end; ++iter) {
            matches.push_back({iter->position(), iter->length()});
        }
        
        // 从后往前替换
        for (auto it = matches.rbegin(); it != matches.rend(); ++it) {
            string numberStr = input.substr(it->first, it->second);
            if (IsNumeric(numberStr)) {
                double number = stod(numberStr);
                string formatted = FormatNumber(number, 2);
                result.replace(it->first, it->second, formatted);
            }
        }
        
        return result;
    }
    
    // 计算文本中所有数字的总和
    pair<double, int> CalculateSum(const string& input) {
        regex numberPattern(R"(\d+(?:\.\d+)?)");
        sregex_iterator iter(input.begin(), input.end(), numberPattern);
        sregex_iterator end;
        
        double total = 0.0;
        int count = 0;
        
        for (; iter != end; ++iter) {
            string numberStr = iter->str();
            if (IsNumeric(numberStr)) {
                total += stod(numberStr);
                count++;
            }
        }
        
        return make_pair(total, count);
    }
    
    // 去除空白字符
    string RemoveBlankSpaces(const string& input) {
        string result = input;
        
        // 去除普通空格
        result = regex_replace(result, regex(" "), "");
        // 去除全角空格
        result = regex_replace(result, regex("　"), "");
        // 去除制表符
        result = regex_replace(result, regex("\t"), "");
        // 去除多余换行
        result = regex_replace(result, regex("\r\n\r\n"), "\r\n");
        result = regex_replace(result, regex("\n\n"), "\n");
        
        return result;
    }
    
    // 从文件读取文本
    string ReadFromFile(const string& filename) {
        ifstream file(filename);
        if (!file.is_open()) {
            return "";
        }
        
        stringstream buffer;
        buffer << file.rdbuf();
        return buffer.str();
    }
    
    // 写入文件
    bool WriteToFile(const string& filename, const string& content) {
        ofstream file(filename);
        if (!file.is_open()) {
            return false;
        }
        
        file << content;
        return true;
    }

private:
    // 检查字符串是否为数字
    bool IsNumeric(const string& str) {
        if (str.empty()) return false;
        
        regex numberPattern(R"(^-?\d+(\.\d+)?$)");
        return regex_match(str, numberPattern);
    }
    
    // 格式化数字（添加千分位符）
    string FormatNumber(double number, int precision = 2) {
        stringstream ss;
        ss << fixed << setprecision(precision) << number;
        string str = ss.str();
        
        // 找到小数点位置
        size_t decimalPos = str.find('.');
        if (decimalPos == string::npos) {
            decimalPos = str.length();
        }
        
        // 从小数点前开始，每三位添加逗号
        for (int i = decimalPos - 3; i > 0; i -= 3) {
            str.insert(i, ",");
        }
        
        return str;
    }
};

// 显示菜单
void ShowMenu() {
    cout << "\n========================================" << endl;
    cout << "数字处理工具 - C++版本" << endl;
    cout << "========================================" << endl;
    cout << "1. 转换为万元格式" << endl;
    cout << "2. 添加千分位符" << endl;
    cout << "3. 计算数字总和" << endl;
    cout << "4. 去除空白字符" << endl;
    cout << "5. 从文件处理" << endl;
    cout << "6. 直接输入文本处理" << endl;
    cout << "0. 退出" << endl;
    cout << "请选择功能 (0-6): ";
}

// 处理文件
void ProcessFile(SimpleNumberProcessor& processor) {
    string inputFile, outputFile;
    cout << "请输入源文件路径: ";
    cin >> inputFile;
    
    string content = processor.ReadFromFile(inputFile);
    if (content.empty()) {
        cout << "无法读取文件或文件为空" << endl;
        return;
    }
    
    cout << "文件内容预览:" << endl;
    cout << content.substr(0, min(200, (int)content.length())) << endl;
    if (content.length() > 200) {
        cout << "... (内容过长，仅显示前200字符)" << endl;
    }
    
    int choice;
    cout << "\n选择处理方式:" << endl;
    cout << "1. 转换为万元  2. 千分位符  3. 去除空白  4. 计算总和" << endl;
    cout << "请选择: ";
    cin >> choice;
    
    string result;
    switch (choice) {
        case 1:
            result = processor.ConvertToWanYuan(content);
            break;
        case 2:
            result = processor.AddThousandsSeparator(content);
            break;
        case 3:
            result = processor.RemoveBlankSpaces(content);
            break;
        case 4: {
            auto sumResult = processor.CalculateSum(content);
            cout << "找到 " << sumResult.second << " 个数字" << endl;
            cout << "总和: " << processor.FormatNumber(sumResult.first, 2) << endl;
            cout << "平均值: " << processor.FormatNumber(sumResult.first / sumResult.second, 2) << endl;
            return;
        }
        default:
            cout << "无效选择" << endl;
            return;
    }
    
    cout << "\n处理结果:" << endl;
    cout << result.substr(0, min(300, (int)result.length())) << endl;
    if (result.length() > 300) {
        cout << "... (结果过长，仅显示前300字符)" << endl;
    }
    
    cout << "\n是否保存到文件? (y/n): ";
    char save;
    cin >> save;
    
    if (save == 'y' || save == 'Y') {
        cout << "请输入输出文件路径: ";
        cin >> outputFile;
        
        if (processor.WriteToFile(outputFile, result)) {
            cout << "文件保存成功: " << outputFile << endl;
        } else {
            cout << "文件保存失败" << endl;
        }
    }
}

// 处理直接输入的文本
void ProcessDirectInput(SimpleNumberProcessor& processor) {
    cout << "请输入要处理的文本 (输入END结束):" << endl;
    
    string input, line;
    cin.ignore(); // 清除缓冲区
    
    while (getline(cin, line) && line != "END") {
        input += line + "\n";
    }
    
    if (input.empty()) {
        cout << "输入为空" << endl;
        return;
    }
    
    int choice;
    cout << "\n选择处理方式:" << endl;
    cout << "1. 转换为万元  2. 千分位符  3. 去除空白  4. 计算总和" << endl;
    cout << "请选择: ";
    cin >> choice;
    
    string result;
    switch (choice) {
        case 1:
            result = processor.ConvertToWanYuan(input);
            cout << "\n转换结果:" << endl << result << endl;
            break;
        case 2:
            result = processor.AddThousandsSeparator(input);
            cout << "\n处理结果:" << endl << result << endl;
            break;
        case 3:
            result = processor.RemoveBlankSpaces(input);
            cout << "\n清理结果:" << endl << result << endl;
            break;
        case 4: {
            auto sumResult = processor.CalculateSum(input);
            cout << "\n计算结果:" << endl;
            cout << "找到 " << sumResult.second << " 个数字" << endl;
            cout << "总和: " << processor.FormatNumber(sumResult.first, 2) << endl;
            if (sumResult.second > 0) {
                cout << "平均值: " << processor.FormatNumber(sumResult.first / sumResult.second, 2) << endl;
            }
            break;
        }
        default:
            cout << "无效选择" << endl;
            return;
    }
}

int main() {
    // 设置控制台编码
    SetConsoleOutputCP(CP_UTF8);
    
    SimpleNumberProcessor processor;
    int choice;
    
    cout << "数字处理工具 - C++独立版本" << endl;
    cout << "无需Word支持，可直接处理文本和文件" << endl;
    
    do {
        ShowMenu();
        cin >> choice;
        
        switch (choice) {
            case 1: {
                string input;
                cout << "请输入数字: ";
                cin >> input;
                cout << "转换结果: " << processor.ConvertToWanYuan(input) << endl;
                break;
            }
            case 2: {
                string input;
                cout << "请输入数字: ";
                cin >> input;
                cout << "处理结果: " << processor.AddThousandsSeparator(input) << endl;
                break;
            }
            case 3: {
                string input;
                cout << "请输入数字: ";
                cin >> input;
                auto result = processor.CalculateSum(input);
                cout << "总和: " << processor.FormatNumber(result.first, 2) << endl;
                cout << "数字个数: " << result.second << endl;
                break;
            }
            case 4: {
                string input;
                cout << "请输入文本: ";
                cin.ignore();
                getline(cin, input);
                cout << "清理结果: " << processor.RemoveBlankSpaces(input) << endl;
                break;
            }
            case 5:
                ProcessFile(processor);
                break;
            case 6:
                ProcessDirectInput(processor);
                break;
            case 0:
                cout << "程序退出" << endl;
                break;
            default:
                cout << "无效选择，请重新输入" << endl;
                break;
        }
    } while (choice != 0);
    
    return 0;
}