@echo off
echo ========================================
echo 数字处理工具 - C++简化版编译脚本
echo ========================================
echo.

echo 正在编译C++程序...

REM 尝试使用g++编译器（如果安装了MinGW）
where g++ >nul 2>nul
if %errorlevel% == 0 (
    echo 使用g++编译器...
    g++ -std=c++17 -O2 SimpleNumberProcessor.cpp -o NumberProcessor.exe
    if %errorlevel% == 0 (
        echo 编译成功！可执行文件: NumberProcessor.exe
        goto :success
    ) else (
        echo g++编译失败，尝试其他编译器...
    )
)

REM 尝试使用cl编译器（Visual Studio）
where cl >nul 2>nul
if %errorlevel% == 0 (
    echo 使用Visual Studio编译器...
    cl /EHsc /std:c++17 SimpleNumberProcessor.cpp /Fe:NumberProcessor.exe
    if %errorlevel% == 0 (
        echo 编译成功！可执行文件: NumberProcessor.exe
        goto :success
    ) else (
        echo Visual Studio编译失败
    )
)

REM 如果都没有找到编译器
echo.
echo 未找到C++编译器！
echo 请安装以下任一编译器：
echo 1. MinGW-w64 (推荐) - https://www.mingw-w64.org/
echo 2. Visual Studio Community (免费) - https://visualstudio.microsoft.com/
echo 3. Code::Blocks with MinGW
echo.
echo 或者直接使用已编译好的VB.NET版本：dist\NumberProcessorTool.exe
pause
exit /b 1

:success
echo.
echo ========================================
echo 编译成功！
echo ========================================
echo.
echo 程序文件: NumberProcessor.exe
echo.
echo 功能特点：
echo - 完全独立运行，无需Word支持
echo - 支持文件批量处理
echo - 支持直接文本输入处理
echo - 数字格式化（万元、千分位符）
echo - 自动计算数字总和和平均值
echo - 文本清理功能
echo.
echo 使用方法：
echo 1. 双击运行 NumberProcessor.exe
echo 2. 根据菜单选择功能
echo 3. 输入数字或文本进行处理
echo 4. 可以处理文件或直接输入文本
echo.
pause