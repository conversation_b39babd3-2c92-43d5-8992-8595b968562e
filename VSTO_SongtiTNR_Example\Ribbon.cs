using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Office.Tools.Ribbon;

namespace WordFontFormatterAddIn
{
    public partial class Ribbon
    {
        private void Ribbon_Load(object sender, RibbonUIEventArgs e)
        {

        }

        /// <summary>
        /// 字体格式化按钮点击事件 - 对应 VBA 的 SongtiTNR(control As IRibbonControl)
        /// </summary>
        private void btnSongtiTNR_Click(object sender, RibbonControlEventArgs e)
        {
            var processor = new FontProcessor();
            processor.ProcessDocumentFonts();
        }

        /// <summary>
        /// 仅设置数字为 Times New Roman
        /// </summary>
        private void btnNumbersOnly_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                var app = Globals.ThisAddIn.Application;
                var selection = app.Selection;
                
                if (selection.Range.Text.Length == 0)
                {
                    System.Windows.Forms.MessageBox.Show("请先选择要处理的文本！", "提示", 
                        System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Information);
                    return;
                }

                app.ScreenUpdating = false;
                
                var processor = new FontProcessor();
                // 这里可以调用私有方法，或者创建一个公共方法来只处理数字
                var find = selection.Range.Find;
                find.ClearFormatting();
                find.Replacement.ClearFormatting();
                
                find.Text = "[0-9.,]+";
                find.Replacement.Text = "^&";
                find.Replacement.Font.Name = "Times New Roman";
                find.Replacement.Font.NameAscii = "Times New Roman";
                find.MatchWildcards = true;
                find.Execute(Replace: Microsoft.Office.Interop.Word.WdReplace.wdReplaceAll);
                
                app.ScreenUpdating = true;
                
                System.Windows.Forms.MessageBox.Show("数字字体已设置为 Times New Roman！", "完成", 
                    System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                System.Windows.Forms.MessageBox.Show($"处理失败：{ex.Message}", "错误", 
                    System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
            }
        }
    }
}