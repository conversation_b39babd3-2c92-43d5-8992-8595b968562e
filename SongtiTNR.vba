Sub SongtiTNR(control As IRibbonControl)  '调整段落格式，调整编号格式和正文格式，数字使用Times New Roman

    ' 创建用户窗体进行统一设置
    Dim result As String
    result = ShowFormatDialog()

    ' 如果用户取消，则退出
    If result = "" Then Exit Sub

    ' 解析返回的设置参数
    Dim settings() As String
    settings = Split(result, "|")

    ' 参数顺序：编号字体|编号字号|编号粗体|正文字体|正文字号|正文粗体
    Dim numberFontName As String, numberFontSize As String, numberIsBold As String
    Dim textFontName As String, textFontSize As String, textIsBold As String

    numberFontName = settings(0)
    numberFontSize = settings(1)
    numberIsBold = settings(2)
    textFontName = settings(3)
    textFontSize = settings(4)
    textIsBold = settings(5)

    Application.ScreenUpdating = False
    Application.DisplayAlerts = wdAlertsNone

    '备份原始选择范围
    Dim originalSelection As Range
    Set originalSelection = Selection.Range.Duplicate

    '1. 修改正文字体格式（优化处理）
    If textFontName <> "" Then
        With Selection.Font
            .Name = textFontName
            .NameFarEast = textFontName
            
            If textFontSize <> "" And IsNumeric(textFontSize) Then
                .Size = CDbl(textFontSize)
            End If
            
            If UCase(textIsBold) = "TRUE" Then
                .Bold = True
            ElseIf UCase(textIsBold) = "FALSE" Then
                .Bold = False
            End If
        End With
    End If

    '2. 修改编号字体格式
    If numberFontName <> "" Then
        Dim processedTemplates As Object
        Set processedTemplates = CreateObject("Scripting.Dictionary")
        
        Dim para As Object
        For Each para In Selection.Paragraphs
            If para.Range.ListFormat.ListType <> wdListNoNumbering Then
                Dim lt As ListTemplate
                Dim currentLevel As Long
                Dim templateKey As String

                Set lt = para.Range.ListFormat.ListTemplate
                currentLevel = para.Range.ListFormat.ListLevelNumber
                templateKey = CStr(ObjPtr(lt)) & "_" & CStr(currentLevel)

                '检查是否已处理过该模板
                If Not processedTemplates.Exists(templateKey) Then
                    If Not lt Is Nothing Then
                        '只修改编号的字体属性
                        With lt.ListLevels(currentLevel).Font
                            .Name = numberFontName
                            .NameFarEast = numberFontName

                            If numberFontSize <> "" And IsNumeric(numberFontSize) Then
                                .Size = CDbl(numberFontSize)
                            End If

                            If UCase(numberIsBold) = "TRUE" Then
                                .Bold = True
                            ElseIf UCase(numberIsBold) = "FALSE" Then
                                .Bold = False
                            End If
                        End With
                        
                        processedTemplates.Add templateKey, True
                    End If
                End If
            End If
        Next para
    End If

    '3. 将正文中的数字字体改为Times New Roman（优化处理）
    Call SetNumbersToTimesNewRoman(Selection.Range)

    '刷新文档
    ActiveDocument.Range.Fields.Update
    originalSelection.Select
    Application.ScreenUpdating = True
    Application.DisplayAlerts = wdAlertsAll

    MsgBox "编号和正文字体已修改完成！数字已设置为Times New Roman。", vbInformation, "设置完成"
End Sub

' 将指定范围内的数字字体设置为Times New Roman（优化版本）
Sub SetNumbersToTimesNewRoman(targetRange As Range)
    With targetRange.Find
        .ClearFormatting
        .Replacement.ClearFormatting
        .Text = "[0-9.,]+"
        .Replacement.Text = "^&"
        .Replacement.Font.Name = "Times New Roman"
        .Replacement.Font.NameAscii = "Times New Roman"
        .Forward = True
        .Wrap = wdFindStop
        .Format = True
        .MatchCase = False
        .MatchWholeWord = False
        .MatchAllWordForms = False
        .MatchSoundsLike = False
        .MatchWildcards = True
        
        ' 执行查找替换直到没有更多匹配项
        Do While .Execute(Replace:=wdReplaceAll)
        Loop
    End With
End Sub

' 显示格式设置对话框的函数
Function ShowFormatDialog() As String
    Dim msg As String
    Dim title As String
    Dim defaultValues As String
    Dim userInput As String

    msg = "请按照以下格式输入设置参数，用英文逗号分隔：" & vbCrLf & vbCrLf & _
          "编号字体名称,编号字号,编号是否粗体(是/否),正文字体名称,正文字号,正文是否粗体(是/否)" & vbCrLf & vbCrLf & _
          "示例：宋体,10.5,否,宋体,10.5,否" & vbCrLf & _
          "说明：字号留空则不修改，如：宋体,,否,宋体,10.5,否" & vbCrLf & _
          "注意：数字将自动设置为Times New Roman字体"

    title = "字体格式设置"
    defaultValues = "宋体,10.5,否,宋体,10.5,否"

    userInput = InputBox(msg, title, defaultValues)

    ' 如果用户取消或输入为空，返回空字符串
    If userInput = "" Then
        ShowFormatDialog = ""
        Exit Function
    End If

    ' 解析用户输入
    Dim inputs() As String
    inputs = Split(userInput, ",")

    ' 检查参数数量
    If UBound(inputs) < 5 Then
        MsgBox "参数数量不足，请按格式输入6个参数！", vbExclamation, "输入错误"
        ShowFormatDialog = ""
        Exit Function
    End If

    ' 处理粗体设置
    Dim numberBold As String, textBold As String

    If UCase(Trim(inputs(2))) = "是" Or UCase(Trim(inputs(2))) = "Y" Or UCase(Trim(inputs(2))) = "YES" Then
        numberBold = "TRUE"
    Else
        numberBold = "FALSE"
    End If

    If UCase(Trim(inputs(5))) = "是" Or UCase(Trim(inputs(5))) = "Y" Or UCase(Trim(inputs(5))) = "YES" Then
        textBold = "TRUE"
    Else
        textBold = "FALSE"
    End If

    ' 返回格式化的参数字符串
    ShowFormatDialog = Trim(inputs(0)) & "|" & Trim(inputs(1)) & "|" & numberBold & "|" & _
                      Trim(inputs(3)) & "|" & Trim(inputs(4)) & "|" & textBold
End Function