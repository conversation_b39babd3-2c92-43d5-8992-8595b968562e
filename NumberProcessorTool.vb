Imports System.Windows.Forms
Imports System.Drawing
Imports System.Text.RegularExpressions

Public Class NumberProcessorForm
    Inherits Form
    
    Private WithEvents txtInput As TextBox
    Private WithEvents txtOutput As TextBox
    Private WithEvents btnUnit10000 As Button
    Private WithEvents btnQianfen As Button
    Private WithEvents btnSum As Button
    Private WithEvents btnClear As Button
    Private WithEvents btnCopy As Button
    Private lblInput As Label
    Private lblOutput As Label
    Private lblStatus As Label
    
    Public Sub New()
        InitializeComponent()
    End Sub
    
    Private Sub InitializeComponent()
        Me.Text = "数字处理工具 - 独立版"
        Me.Size = New Size(600, 500)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.MinimumSize = New Size(500, 400)
        
        ' 输入标签
        lblInput = New Label With {
            .Text = "输入数字或文本（可以包含多个数字）：",
            .Size = New Size(300, 20),
            .Location = New Point(20, 20)
        }
        
        ' 输入文本框
        txtInput = New TextBox With {
            .Size = New Size(540, 100),
            .Location = New Point(20, 45),
            .Multiline = True,
            .ScrollBars = ScrollBars.Vertical,
            .Font = New Font("Consolas", 10)
        }
        
        ' 按钮区域
        btnUnit10000 = New Button With {
            .Text = "转换为万元",
            .Size = New Size(100, 35),
            .Location = New Point(20, 160)
        }
        
        btnQianfen = New Button With {
            .Text = "添加千分位",
            .Size = New Size(100, 35),
            .Location = New Point(130, 160)
        }
        
        btnSum = New Button With {
            .Text = "计算总和",
            .Size = New Size(100, 35),
            .Location = New Point(240, 160)
        }
        
        btnClear = New Button With {
            .Text = "清空",
            .Size = New Size(80, 35),
            .Location = New Point(350, 160)
        }
        
        btnCopy = New Button With {
            .Text = "复制结果",
            .Size = New Size(100, 35),
            .Location = New Point(440, 160)
        }
        
        ' 输出标签
        lblOutput = New Label With {
            .Text = "处理结果：",
            .Size = New Size(200, 20),
            .Location = New Point(20, 210)
        }
        
        ' 输出文本框
        txtOutput = New TextBox With {
            .Size = New Size(540, 100),
            .Location = New Point(20, 235),
            .Multiline = True,
            .ScrollBars = ScrollBars.Vertical,
            .ReadOnly = True,
            .Font = New Font("Consolas", 10),
            .BackColor = Color.LightGray
        }
        
        ' 状态标签
        lblStatus = New Label With {
            .Text = "状态：等待操作",
            .Size = New Size(540, 25),
            .Location = New Point(20, 350),
            .ForeColor = Color.Blue,
            .BorderStyle = BorderStyle.FixedSingle,
            .TextAlign = ContentAlignment.MiddleLeft
        }
        
        ' 添加示例文本
        txtInput.Text = "示例：输入数字如 12345.67 或包含数字的文本" & vbCrLf & "支持多行文本，程序会自动识别其中的数字"
        
        ' 添加控件到窗体
        Me.Controls.AddRange({lblInput, txtInput, btnUnit10000, btnQianfen, btnSum, btnClear, btnCopy, lblOutput, txtOutput, lblStatus})
    End Sub
    
    Private Sub btnUnit10000_Click(sender As Object, e As EventArgs) Handles btnUnit10000.Click
        Try
            lblStatus.Text = "状态：正在转换为万元格式..."
            lblStatus.ForeColor = Color.Orange
            Application.DoEvents()
            
            Dim inputText As String = txtInput.Text
            If String.IsNullOrWhiteSpace(inputText) Then
                MessageBox.Show("请输入要处理的数字", "提示")
                lblStatus.Text = "状态：输入为空"
                lblStatus.ForeColor = Color.Red
                Return
            End If
            
            ' 使用正则表达式找到所有数字
            Dim pattern As String = "\d+(?:\.\d+)?"
            Dim matches As MatchCollection = Regex.Matches(inputText, pattern)
            
            If matches.Count = 0 Then
                MessageBox.Show("未找到有效数字", "提示")
                lblStatus.Text = "状态：未找到数字"
                lblStatus.ForeColor = Color.Red
                Return
            End If
            
            Dim result As String = inputText
            Dim processedCount As Integer = 0
            
            ' 从后往前替换，避免位置偏移
            For i As Integer = matches.Count - 1 To 0 Step -1
                Dim match As Match = matches(i)
                If IsNumeric(match.Value) Then
                    Dim number As Double = CDbl(match.Value)
                    Dim formatted As String = Format(Math.Round(number, 2), "#,##0.00") & "万"
                    result = result.Substring(0, match.Index) & formatted & result.Substring(match.Index + match.Length)
                    processedCount += 1
                End If
            Next
            
            txtOutput.Text = result
            lblStatus.Text = $"状态：转换完成，处理了 {processedCount} 个数字"
            lblStatus.ForeColor = Color.Green
            
        Catch ex As Exception
            MessageBox.Show($"转换失败：{ex.Message}", "错误")
            lblStatus.Text = $"状态：转换失败 - {ex.Message}"
            lblStatus.ForeColor = Color.Red
        End Try
    End Sub
    
    Private Sub btnQianfen_Click(sender As Object, e As EventArgs) Handles btnQianfen.Click
        Try
            lblStatus.Text = "状态：正在添加千分位符..."
            lblStatus.ForeColor = Color.Orange
            Application.DoEvents()
            
            Dim inputText As String = txtInput.Text
            If String.IsNullOrWhiteSpace(inputText) Then
                MessageBox.Show("请输入要处理的数字", "提示")
                lblStatus.Text = "状态：输入为空"
                lblStatus.ForeColor = Color.Red
                Return
            End If
            
            ' 使用正则表达式找到所有数字
            Dim pattern As String = "\d+(?:\.\d+)?"
            Dim matches As MatchCollection = Regex.Matches(inputText, pattern)
            
            If matches.Count = 0 Then
                MessageBox.Show("未找到有效数字", "提示")
                lblStatus.Text = "状态：未找到数字"
                lblStatus.ForeColor = Color.Red
                Return
            End If
            
            Dim result As String = inputText
            Dim processedCount As Integer = 0
            
            ' 从后往前替换，避免位置偏移
            For i As Integer = matches.Count - 1 To 0 Step -1
                Dim match As Match = matches(i)
                If IsNumeric(match.Value) Then
                    Dim number As Double = CDbl(match.Value)
                    Dim formatted As String = Format(number, "#,##0.00")
                    result = result.Substring(0, match.Index) & formatted & result.Substring(match.Index + match.Length)
                    processedCount += 1
                End If
            Next
            
            txtOutput.Text = result
            lblStatus.Text = $"状态：千分位添加完成，处理了 {processedCount} 个数字"
            lblStatus.ForeColor = Color.Green
            
        Catch ex As Exception
            MessageBox.Show($"处理失败：{ex.Message}", "错误")
            lblStatus.Text = $"状态：处理失败 - {ex.Message}"
            lblStatus.ForeColor = Color.Red
        End Try
    End Sub
    
    Private Sub btnSum_Click(sender As Object, e As EventArgs) Handles btnSum.Click
        Try
            lblStatus.Text = "状态：正在计算总和..."
            lblStatus.ForeColor = Color.Orange
            Application.DoEvents()
            
            Dim inputText As String = txtInput.Text
            If String.IsNullOrWhiteSpace(inputText) Then
                MessageBox.Show("请输入要计算的数字", "提示")
                lblStatus.Text = "状态：输入为空"
                lblStatus.ForeColor = Color.Red
                Return
            End If
            
            ' 使用正则表达式找到所有数字
            Dim pattern As String = "\d+(?:\.\d+)?"
            Dim matches As MatchCollection = Regex.Matches(inputText, pattern)
            
            If matches.Count = 0 Then
                MessageBox.Show("未找到有效数字", "提示")
                lblStatus.Text = "状态：未找到数字"
                lblStatus.ForeColor = Color.Red
                Return
            End If
            
            Dim total As Double = 0
            Dim numbers As New List(Of String)
            
            For Each match As Match In matches
                If IsNumeric(match.Value) Then
                    Dim number As Double = CDbl(match.Value)
                    total += number
                    numbers.Add(Format(number, "#,##0.00"))
                End If
            Next
            
            Dim result As String = "找到的数字：" & vbCrLf & String.Join(" + ", numbers) & vbCrLf & vbCrLf & 
                                  $"总和：{Format(total, "#,##0.00")}" & vbCrLf & 
                                  $"平均值：{Format(total / numbers.Count, "#,##0.00")}" & vbCrLf & 
                                  $"数字个数：{numbers.Count}"
            
            txtOutput.Text = result
            lblStatus.Text = $"状态：计算完成，总和：{Format(total, "#,##0.00")}"
            lblStatus.ForeColor = Color.Green
            
        Catch ex As Exception
            MessageBox.Show($"计算失败：{ex.Message}", "错误")
            lblStatus.Text = $"状态：计算失败 - {ex.Message}"
            lblStatus.ForeColor = Color.Red
        End Try
    End Sub
    
    Private Sub btnClear_Click(sender As Object, e As EventArgs) Handles btnClear.Click
        txtInput.Clear()
        txtOutput.Clear()
        lblStatus.Text = "状态：已清空"
        lblStatus.ForeColor = Color.Blue
        txtInput.Focus()
    End Sub
    
    Private Sub btnCopy_Click(sender As Object, e As EventArgs) Handles btnCopy.Click
        Try
            If Not String.IsNullOrWhiteSpace(txtOutput.Text) Then
                Clipboard.SetText(txtOutput.Text)
                lblStatus.Text = "状态：结果已复制到剪贴板"
                lblStatus.ForeColor = Color.Green
            Else
                MessageBox.Show("没有可复制的内容", "提示")
                lblStatus.Text = "状态：没有可复制的内容"
                lblStatus.ForeColor = Color.Red
            End If
        Catch ex As Exception
            MessageBox.Show($"复制失败：{ex.Message}", "错误")
            lblStatus.Text = $"状态：复制失败 - {ex.Message}"
            lblStatus.ForeColor = Color.Red
        End Try
    End Sub
End Class

Module NumberProcessorProgram
    <STAThread>
    Sub Main()
        System.Windows.Forms.Application.EnableVisualStyles()
        System.Windows.Forms.Application.SetCompatibleTextRenderingDefault(False)
        System.Windows.Forms.Application.Run(New NumberProcessorForm())
    End Sub
End Module