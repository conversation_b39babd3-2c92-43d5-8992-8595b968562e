“Word”: 不是类或命名空间名称
后面有“::”的名称一定是类名或命名空间名
后面有“::”的名称一定是类名或命名空间名
不允许使用不完整的类型 "void"
后面有“::”的名称一定是类名或命名空间名
应输入“)”
不允许使用不完整的类型 "void"
后面有“::”的名称一定是类名或命名空间名
应输入“)”
应输入“;”
应输入声明
此声明没有存储类或类型说明符
此声明没有存储类或类型说明符
应输入“;”
此声明没有存储类或类型说明符
应输入“;”
此声明没有存储类或类型说明符
应输入声明
应输入声明
“Word”: 不是类或命名空间名称
语法错误: 标识符“SelectionPtr”
语法错误: 标识符“SelectionPtr”
“Word”: 不是类或命名空间名称
“SetNumbersToTimesNewRomanOptimized”: “void” 这一使用无效
“RangePtr”: 未声明的标识符
语法错误: 缺少“)”(在标识符“targetRange”的前面)
“Word”: 不是类或命名空间名称
“SongtiTNR”: “void” 这一使用无效
“SelectionPtr”: 未声明的标识符
语法错误: 缺少“)”(在标识符“selection”的前面)
语法错误: 缺少“;”(在“{”的前面)
“{”: 缺少函数标题(是否是老式的形式表?)
“Word”: 不是类或命名空间名称
语法错误: 标识符“SelectionPtr”
“selection”: 未声明的标识符
“Word”: 不是类或命名空间名称
“ParagraphPtr”: 未声明的标识符
语法错误: 缺少“;”(在标识符“para”的前面)
“para”: 未声明的标识符
“selection”: 未声明的标识符
“para”: 未声明的标识符
“wdListNoNumbering”: 未声明的标识符
“Word”: 不是类或命名空间名称
“ListTemplatePtr”: 未声明的标识符
语法错误: 缺少“;”(在标识符“lt”的前面)
“lt”: 未声明的标识符
“para”: 未声明的标识符
“para”: 未声明的标识符
“lt”: 未声明的标识符
“sprintf_s”: 格式字符串“%p”需要类型“void *”的参数，但可变参数 1 拥有了类型“long”
"sprintf_s": 格式字符串中的 "%p" 与类型为 "long" 的参数 1 冲突
“sprintf_s”: 没有为格式字符串传递足够的参数
“lt”: 未声明的标识符
“Word”: 不是类或命名空间名称
“lt”: 未声明的标识符
"Name": 不是 "Font" 的成员
"NameFarEast": 不是 "Font" 的成员
"Size": 不是 "Font" 的成员
"Bold": 不是 "Font" 的成员
“Word”: 不是类或命名空间名称
语法错误: 标识符“SelectionPtr”
“Word”: 不是类或命名空间名称
“selection”: 未声明的标识符
"Name": 不是 "Font" 的成员
"NameFarEast": 不是 "Font" 的成员
"Size": 不是 "Font" 的成员
"Bold": 不是 "Font" 的成员
“Word”: 不是类或命名空间名称
“SetNumbersToTimesNewRomanOptimized”: “void” 这一使用无效
“SetNumbersToTimesNewRomanOptimized”: 重定义；多次初始化
“int SetNumbersToTimesNewRomanOptimized”: 重定义
“RangePtr”: 未声明的标识符
语法错误: 缺少“)”(在标识符“targetRange”的前面)
语法错误: 缺少“;”(在“{”的前面)
“{”: 缺少函数标题(是否是老式的形式表?)
“ss”使用未定义的 class“std::basic_stringstream<char,std::char_traits<char>,std::allocator<char>>”
“getline”: 未找到匹配的重载函数