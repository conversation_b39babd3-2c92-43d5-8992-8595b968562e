Sub FormatTableWithSmartWidth()
    '智能表格格式化 - 根据文字内容自动调整列宽
    
    Dim myTable As Table
    
    ' 检查是否在表格中
    If Selection.Information(wdWithInTable) Then
        Set myTable = Selection.Tables(1)
    Else
        MsgBox "请将光标放在表格中再运行此宏。"
        Exit Sub
    End If
    
    Application.ScreenUpdating = False
    
    ' === 第一步：智能调整列宽 ===
    Call OptimizedColumnWidths(myTable)
    
    ' === 第二步：设置边框和格式 ===
    With myTable
        ' 设置边框样式
        .Borders(wdBorderLeft).LineStyle = wdLineStyleNone
        .Borders(wdBorderRight).LineStyle = wdLineStyleNone
        
        With .Borders(wdBorderTop)
            .LineStyle = wdLineStyleDouble
            .LineWidth = wdLineWidth050pt
        End With
        
        With .Borders(wdBorderBottom)
            .LineStyle = wdLineStyleDouble
            .LineWidth = wdLineWidth050pt
        End With
        
        With .Borders(wdBorderHorizontal)
            .LineStyle = wdLineStyleSingle
            .LineWidth = wdLineWidth050pt
        End With
        
        With .Borders(wdBorderVertical)
            .LineStyle = wdLineStyleSingle
            .LineWidth = wdLineWidth050pt
        End With
        
        ' 设置表格整体宽度
        .PreferredWidthType = wdPreferredWidthPercent
        .PreferredWidth = 100
        
        ' 最后微调以适应页面
        .AutoFitBehavior wdAutoFitWindow
    End With
    
    Application.ScreenUpdating = True
    MsgBox "表格格式化完成！列宽已根据内容自动调整。"
End Sub

'-----------------------------------------------------------
' 智能列宽调整函数
Private Sub AutoAdjustColumnWidths(tbl As Table)
    On Error Resume Next
    
    Dim i As Long, j As Long
    Dim colWidths() As Long
    Dim maxTextLength() As Long
    Dim cellText As String
    Dim totalCols As Long
    Dim totalWidth As Long
    Dim minWidth As Long
    Dim maxWidth As Long
    
    totalCols = tbl.Columns.Count
    ReDim colWidths(1 To totalCols)
    ReDim maxTextLength(1 To totalCols)
    
    ' 分析每列的最大文本长度
    For j = 1 To totalCols
        maxTextLength(j) = 0
        
        For i = 1 To tbl.Rows.Count
            cellText = Left(tbl.Cell(i, j).Range.Text, Len(tbl.Cell(i, j).Range.Text) - 2)
            cellText = Trim(cellText)
            
            ' 计算文本显示长度（中文字符按2个字符计算）
            Dim displayLength As Long
            displayLength = GetDisplayLength(cellText)
            
            If displayLength > maxTextLength(j) Then
                maxTextLength(j) = displayLength
            End If
        Next i
    Next j
    
    ' 计算总的文本长度
    totalWidth = 0
    For j = 1 To totalCols
        totalWidth = totalWidth + maxTextLength(j)
    Next j
    
    ' 设置最小和最大列宽限制
    minWidth = 8  ' 最小8%
    maxWidth = 50 ' 最大50%
    
    ' 根据文本长度按比例分配列宽
    For j = 1 To totalCols
        If totalWidth > 0 Then
            colWidths(j) = Int((maxTextLength(j) / totalWidth) * 100)
        Else
            colWidths(j) = Int(100 / totalCols)
        End If
        
        ' 应用最小和最大宽度限制
        If colWidths(j) < minWidth Then colWidths(j) = minWidth
        If colWidths(j) > maxWidth Then colWidths(j) = maxWidth
    Next j
    
    ' 调整总宽度为100%
    Call NormalizeWidths(colWidths, totalCols)
    
    ' 应用列宽设置
    For j = 1 To totalCols
        tbl.Columns(j).PreferredWidthType = wdPreferredWidthPercent
        tbl.Columns(j).PreferredWidth = colWidths(j)
    Next j
    
    Err.Clear
End Sub

'-----------------------------------------------------------
' 计算文本显示长度（中文字符按2个字符计算）
Private Function GetDisplayLength(text As String) As Long
    Dim i As Long
    Dim length As Long
    Dim char As String
    
    length = 0
    For i = 1 To Len(text)
        char = Mid(text, i, 1)
        If Asc(char) > 127 Then
            ' 中文字符
            length = length + 2
        Else
            ' 英文字符
            length = length + 1
        End If
    Next i
    
    GetDisplayLength = length
End Function

'-----------------------------------------------------------
' 标准化列宽，确保总和为100%
Private Sub NormalizeWidths(ByRef widths() As Long, totalCols As Long)
    Dim i As Long
    Dim currentTotal As Long
    Dim difference As Long
    
    ' 计算当前总宽度
    currentTotal = 0
    For i = 1 To totalCols
        currentTotal = currentTotal + widths(i)
    Next i
    
    ' 调整到100%
    difference = 100 - currentTotal
    
    If difference <> 0 Then
        ' 将差值平均分配到各列
        Dim adjustment As Long
        adjustment = Int(difference / totalCols)
        
        For i = 1 To totalCols
            widths(i) = widths(i) + adjustment
        Next i
        
        ' 处理余数
        Dim remainder As Long
        remainder = difference - (adjustment * totalCols)
        
        ' 将余数分配给前几列
        For i = 1 To Abs(remainder)
            If remainder > 0 Then
                widths(i) = widths(i) + 1
            Else
                widths(i) = widths(i) - 1
            End If
        Next i
    End If
End Sub

'-----------------------------------------------------------
' 针对您的表格结构优化的版本
Private Sub OptimizedColumnWidths(tbl As Table)
    '针对"序号、地块位置、账面原值、现状/备注"结构优化
    
    On Error Resume Next
    
    Dim totalCols As Long
    totalCols = tbl.Columns.Count
    
    Select Case totalCols
        Case 4
            ' 四列表格：根据图片中的内容优化
            ' 分析每列的实际内容长度
            Dim col1MaxLen As Long, col2MaxLen As Long, col3MaxLen As Long, col4MaxLen As Long
            Dim i As Long
            Dim cellText As String
            
            ' 分析第一列（序号）
            col1MaxLen = 0
            For i = 1 To tbl.Rows.Count
                cellText = Trim(Left(tbl.Cell(i, 1).Range.Text, Len(tbl.Cell(i, 1).Range.Text) - 2))
                If GetDisplayLength(cellText) > col1MaxLen Then
                    col1MaxLen = GetDisplayLength(cellText)
                End If
            Next i
            
            ' 分析第二列（地块位置）
            col2MaxLen = 0
            For i = 1 To tbl.Rows.Count
                cellText = Trim(Left(tbl.Cell(i, 2).Range.Text, Len(tbl.Cell(i, 2).Range.Text) - 2))
                If GetDisplayLength(cellText) > col2MaxLen Then
                    col2MaxLen = GetDisplayLength(cellText)
                End If
            Next i
            
            ' 分析第三列（账面原值）
            col3MaxLen = 0
            For i = 1 To tbl.Rows.Count
                cellText = Trim(Left(tbl.Cell(i, 3).Range.Text, Len(tbl.Cell(i, 3).Range.Text) - 2))
                If GetDisplayLength(cellText) > col3MaxLen Then
                    col3MaxLen = GetDisplayLength(cellText)
                End If
            Next i
            
            ' 分析第四列（现状/备注）
            col4MaxLen = 0
            For i = 1 To tbl.Rows.Count
                cellText = Trim(Left(tbl.Cell(i, 4).Range.Text, Len(tbl.Cell(i, 4).Range.Text) - 2))
                If GetDisplayLength(cellText) > col4MaxLen Then
                    col4MaxLen = GetDisplayLength(cellText)
                End If
            Next i
            
            ' 根据内容长度智能分配
            Dim totalLen As Long
            totalLen = col1MaxLen + col2MaxLen + col3MaxLen + col4MaxLen
            
            If totalLen > 0 Then
                ' 按比例分配，但设置合理的最小和最大值
                Dim width1 As Long, width2 As Long, width3 As Long, width4 As Long
                
                width1 = Int((col1MaxLen / totalLen) * 100)
                width2 = Int((col2MaxLen / totalLen) * 100)
                width3 = Int((col3MaxLen / totalLen) * 100)
                width4 = Int((col4MaxLen / totalLen) * 100)
                
                ' 设置合理的范围
                If width1 < 8 Then width1 = 8
                If width1 > 15 Then width1 = 15
                
                If width2 < 20 Then width2 = 20
                If width2 > 40 Then width2 = 40
                
                If width3 < 15 Then width3 = 15
                If width3 > 30 Then width3 = 30
                
                ' 第四列获得剩余空间
                width4 = 100 - width1 - width2 - width3
                If width4 < 15 Then
                    ' 如果第四列太小，重新调整
                    width4 = 15
                    Dim remaining As Long
                    remaining = 100 - width4
                    width1 = Int(remaining * 0.1)
                    width2 = Int(remaining * 0.45)
                    width3 = remaining - width1 - width2
                End If
                
                ' 应用列宽
                tbl.Columns(1).PreferredWidthType = wdPreferredWidthPercent
                tbl.Columns(1).PreferredWidth = width1
                tbl.Columns(2).PreferredWidthType = wdPreferredWidthPercent
                tbl.Columns(2).PreferredWidth = width2
                tbl.Columns(3).PreferredWidthType = wdPreferredWidthPercent
                tbl.Columns(3).PreferredWidth = width3
                tbl.Columns(4).PreferredWidthType = wdPreferredWidthPercent
                tbl.Columns(4).PreferredWidth = width4
            Else
                ' 默认分配
                tbl.Columns(1).PreferredWidthType = wdPreferredWidthPercent
                tbl.Columns(1).PreferredWidth = 10
                tbl.Columns(2).PreferredWidthType = wdPreferredWidthPercent
                tbl.Columns(2).PreferredWidth = 35
                tbl.Columns(3).PreferredWidthType = wdPreferredWidthPercent
                tbl.Columns(3).PreferredWidth = 25
                tbl.Columns(4).PreferredWidthType = wdPreferredWidthPercent
                tbl.Columns(4).PreferredWidth = 30
            End If
            
        Case Else
            ' 其他列数使用通用算法
            Call AutoAdjustColumnWidths(tbl)
    End Select
    
    Err.Clear
End Sub

'-----------------------------------------------------------
' 完整的表格格式化函数（包含智能列宽）
Sub FormatTableWithAutoWidth()
    '表格格式化 - 包含智能列宽调整
    
    Dim myTable As Table
    
    ' 检查是否在表格中
    If Selection.Information(wdWithInTable) Then
        Set myTable = Selection.Tables(1)
    Else
        MsgBox "请将光标放在表格中再运行此宏。"
        Exit Sub
    End If
    
    Application.ScreenUpdating = False
    
    With myTable
        ' 第一步：智能调整列宽
        Call OptimizedColumnWidths(myTable)
        
        ' 第二步：设置边框
        .Borders(wdBorderLeft).LineStyle = wdLineStyleNone
        .Borders(wdBorderRight).LineStyle = wdLineStyleNone
        
        With .Borders(wdBorderTop)
            .LineStyle = wdLineStyleDouble
            .LineWidth = wdLineWidth050pt
        End With
        
        With .Borders(wdBorderBottom)
            .LineStyle = wdLineStyleDouble
            .LineWidth = wdLineWidth050pt
        End With
        
        With .Borders(wdBorderHorizontal)
            .LineStyle = wdLineStyleSingle
            .LineWidth = wdLineWidth050pt
        End With
        
        With .Borders(wdBorderVertical)
            .LineStyle = wdLineStyleSingle
            .LineWidth = wdLineWidth050pt
        End With
        
        ' 第三步：设置表格整体属性
        .PreferredWidthType = wdPreferredWidthPercent
        .PreferredWidth = 100
        
        ' 第四步：最后微调以适应页面
        .AutoFitBehavior wdAutoFitWindow
    End With
    
    Application.ScreenUpdating = True
    MsgBox "表格格式化完成！列宽已根据内容自动调整。"
End Sub