@echo off
echo Word格式化工具 - 快速编译

REM 检查源文件
if not exist "WordFormatter_Clean.cpp" (
    echo 错误：找不到 WordFormatter_Clean.cpp 文件
    echo 请确保文件在当前目录中
    pause
    exit /b 1
)

echo 正在编译...

REM 直接尝试使用cl编译器（假设已经在VS命令提示符中）
cl /EHsc /std:c++17 /utf-8 /D_WIN32_DCOM /DUNICODE /D_UNICODE /D_CRT_SECURE_NO_WARNINGS WordFormatter_Clean.cpp /link ole32.lib oleaut32.lib uuid.lib user32.lib kernel32.lib /OUT:WordFormatter.exe 2>compile_error.log

if exist WordFormatter.exe (
    echo.
    echo 编译成功！
    echo 可执行文件: WordFormatter.exe
    echo.
    echo 使用说明：
    echo 1. 打开Word，选择文本
    echo 2. 运行 WordFormatter.exe
    echo 3. 输入参数: 宋体,10.5,否,宋体,10.5,否
    if exist compile_error.log del compile_error.log
) else (
    echo.
    echo 编译失败！
    echo.
    if exist compile_error.log (
        echo 错误信息：
        type compile_error.log
        echo.
    )
    echo 解决方案：
    echo 1. 在"开始菜单"中搜索"Developer Command Prompt"
    echo 2. 或搜索"x64 Native Tools Command Prompt"
    echo 3. 在该命令提示符中运行此脚本
    echo.
    echo 或者安装MinGW：
    echo winget install mingw
)

pause