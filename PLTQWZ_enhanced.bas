Sub PLTQWZ(control As IRibbonControl)
    '从word文档中批量提取文字 - 支持多关键字查找
    Dim doc As Document
    Dim newDoc As Document
    Dim fileDialog As fileDialog
    Dim keywordInput As String
    Dim keywords() As String
    Dim i As Long, j <PERSON> Long
    Dim foundContent As String
    Dim hasFoundAny As Boolean
    
    ' 设置要查找的关键字（支持多个，用逗号分隔）
    keywordInput = InputBox("请输入要查找的关键字（多个关键字用英文逗号分隔）:" & _
        vbCrLf & "例如：合同,协议,项目,建设", "关键字输入")
    
    ' 检查用户是否取消输入
    If Len(Trim$(keywordInput)) = 0 Then
        MsgBox "未输入关键字，操作已取消。", vbInformation, "提示"
        Exit Sub
    End If
    
    ' 分割关键字
    keywords = Split(keywordInput, ",")
    
    ' 清理关键字数组，去除空格
    For i = LBound(keywords) To UBound(keywords)
        keywords(i) = Trim$(keywords(i))
    Next i
    
    ' 创建文件对话框以选择文件
    Set fileDialog = Application.fileDialog(msoFileDialogFilePicker)
    fileDialog.AllowMultiSelect = True
    fileDialog.title = "选择要读取的 Word 文档"
    fileDialog.Filters.Clear
    fileDialog.Filters.Add "Word 文档", "*.doc; *.docx"
    
    ' 显示对话框并获取用户选择的文件
    If fileDialog.Show = -1 Then
        ' 创建一个新的 Word 文档
        Set newDoc = Documents.Add
        
        ' 添加标题
        newDoc.Content.InsertAfter "批量文字提取结果" & vbCrLf
        newDoc.Content.InsertAfter "查找关键字：" & keywordInput & vbCrLf
        newDoc.Content.InsertAfter "提取时间：" & Now() & vbCrLf
        newDoc.Content.InsertAfter String(50, "=") & vbCrLf & vbCrLf
        
        ' 循环打开每个选择的文件
        For i = 1 To fileDialog.SelectedItems.Count
            Set doc = Documents.Open(fileDialog.SelectedItems(i))
            foundContent = ""
            hasFoundAny = False
            
            ' 检查文档中是否包含任何关键字
            For j = LBound(keywords) To UBound(keywords)
                If Len(keywords(j)) > 0 Then
                    If InStr(1, doc.Content.text, keywords(j), vbTextCompare) > 0 Then
                        If hasFoundAny Then
                            foundContent = foundContent & ", "
                        End If
                        foundContent = foundContent & keywords(j)
                        hasFoundAny = True
                    End If
                End If
            Next j
            
            ' 如果找到任何关键字，将结果写入新文档
            If hasFoundAny Then
                newDoc.Content.InsertAfter "文件名: " & doc.Name & vbCrLf
                newDoc.Content.InsertAfter "文件路径: " & doc.FullName & vbCrLf
                newDoc.Content.InsertAfter "找到的关键字: " & foundContent & vbCrLf
                
                ' 提取包含关键字的段落内容
                Call ExtractParagraphsWithKeywords(doc, newDoc, keywords)
                
                newDoc.Content.InsertAfter String(30, "-") & vbCrLf & vbCrLf
            End If
            
            doc.Close SaveChanges:=False
        Next i
        
        ' 添加统计信息
        newDoc.Content.InsertAfter vbCrLf & "提取完成！" & vbCrLf
        newDoc.Content.InsertAfter "共处理文件：" & fileDialog.SelectedItems.Count & " 个" & vbCrLf
        
        ' 显示新文档
        newDoc.Activate
        
        ' 格式化文档
        Call FormatResultDocument(newDoc)
        
    Else
        MsgBox "未选择文件，操作已取消。", vbInformation, "提示"
        Exit Sub
    End If
End Sub

'-----------------------------------------------------------
' 提取包含关键字的段落内容
Private Sub ExtractParagraphsWithKeywords(sourceDoc As Document, targetDoc As Document, keywords() As String)
    Dim para As Paragraph
    Dim j As Long
    Dim hasKeyword As Boolean
    Dim paraText As String
    Dim cleanText As String
    
    targetDoc.Content.InsertAfter "相关段落内容:" & vbCrLf
    
    ' 遍历源文档的每个段落
    For Each para In sourceDoc.Paragraphs
        ' 获取段落文本并清理特殊字符
        paraText = para.Range.text
        cleanText = CleanParagraphText(paraText)
        hasKeyword = False
        
        ' 检查段落是否包含任何关键字
        For j = LBound(keywords) To UBound(keywords)
            If Len(keywords(j)) > 0 Then
                If InStr(1, cleanText, keywords(j), vbTextCompare) > 0 Then
                    hasKeyword = True
                    Exit For
                End If
            End If
        Next j
        
        ' 如果段落包含关键字且不为空，则添加到结果文档
        If hasKeyword And Len(Trim$(cleanText)) > 1 Then
            targetDoc.Content.InsertAfter "  • " & cleanText & vbCrLf
        End If
    Next para
    
    targetDoc.Content.InsertAfter vbCrLf
End Sub

'-----------------------------------------------------------
' 清理段落文本中的特殊字符
Private Function CleanParagraphText(rawText As String) As String
    Dim cleanedText As String
    cleanedText = rawText
    
    ' 移除段落标记符（ASCII 13）
    cleanedText = Replace(cleanedText, Chr(13), "")
    
    ' 移除换行符（ASCII 10）
    cleanedText = Replace(cleanedText, Chr(10), "")
    
    ' 移除制表符
    cleanedText = Replace(cleanedText, Chr(9), " ")
    
    ' 移除其他不可见字符
    cleanedText = Replace(cleanedText, Chr(7), "")  ' 响铃符
    cleanedText = Replace(cleanedText, Chr(12), "") ' 换页符
    cleanedText = Replace(cleanedText, Chr(11), "") ' 垂直制表符
    
    ' 移除Word特殊字符
    cleanedText = Replace(cleanedText, Chr(160), " ") ' 不间断空格
    cleanedText = Replace(cleanedText, Chr(8203), "") ' 零宽空格
    
    ' 清理多余的空格
    Do While InStr(cleanedText, "  ") > 0
        cleanedText = Replace(cleanedText, "  ", " ")
    Loop
    
    ' 去除首尾空格
    cleanedText = Trim$(cleanedText)
    
    CleanParagraphText = cleanedText
End Function

'-----------------------------------------------------------
' 格式化结果文档
Private Sub FormatResultDocument(doc As Document)
    On Error Resume Next
    
    ' 设置字体
    doc.Range.Font.Name = "微软雅黑"
    doc.Range.Font.Size = 10
    
    ' 格式化标题
    Dim titleRange As Range
    Set titleRange = doc.Paragraphs(1).Range
    titleRange.Font.Size = 14
    titleRange.Font.Bold = True
    titleRange.ParagraphFormat.Alignment = wdAlignParagraphCenter
    
    ' 设置页边距
    doc.PageSetup.TopMargin = CentimetersToPoints(2)
    doc.PageSetup.BottomMargin = CentimetersToPoints(2)
    doc.PageSetup.LeftMargin = CentimetersToPoints(2)
    doc.PageSetup.RightMargin = CentimetersToPoints(2)
    
    On Error GoTo 0
End Sub