Sub tuomin(control As IRibbonControl) '对文字材料进行脱敏

' 添加确认对话框，防止意外执行
Dim response As VbMsgBoxResult
response = MsgBox("确定要对当前文档进行脱敏处理吗？此操作不可撤销。" & vbCrLf & _
                  "建议先备份文档。点击'是'继续，'否'或'取消'将中止操作。", _
                  vbYesNoCancel + vbQuestion + vbDefaultButton3, "脱敏操作确认")

' 如果用户选择否或取消，则退出子程序
If response <> vbYes Then
    MsgBox "脱敏操作已中止。", vbInformation, "操作中止"
    Exit Sub
End If

Application.ScreenUpdating = False
Dim doc As Document: Set doc = ActiveDocument
Dim totalReplacements As Long: totalReplacements = 0
Dim originalPages As Long: originalPages = doc.ComputeStatistics(wdStatisticPages)

' -------- 机构名脱敏 --------
Call SelectiveReplace("([一-龥]{6})([一-龥]{0,20})公司", "XXXXXX$2公司", totalReplacements)
Call SelectiveReplace("([一-龥]{6})([一-龥]{0,20})有限公司", "XXXXXX$2有限公司", totalReplacements)
Call SelectiveReplace("([一-龥]{6})([一-龥]{0,20})股份有限公司", "XXXXXX$2股份有限公司", totalReplacements)
Call SelectiveReplace("([一-龥]{6})([一-龥]{0,20})学院", "XXXXXX$2学院", totalReplacements)
Call SelectiveReplace("([一-龥]{6})([一-龥]{0,20})学校", "XXXXXX$2学校", totalReplacements)
Call SelectiveReplace("([一-龥]{6})([一-龥]{0,20})大学", "XXXXXX$2大学", totalReplacements)
Call SelectiveReplace("([一-龥]{6})([一-龥]{0,20})局", "XXXXXX$2局", totalReplacements)
Call SelectiveReplace("([一-龥]{6})([一-龥]{0,20})院", "XXXXXX$2院", totalReplacements)

Call SelectiveReplace("([一-龥]{1,5})公司", "XXX公司", totalReplacements)
Call SelectiveReplace("([一-龥]{1,5})有限公司", "XXX有限公司", totalReplacements)
Call SelectiveReplace("([一-龥]{1,5})股份有限公司", "XXX股份有限公司", totalReplacements)
Call SelectiveReplace("([一-龥]{1,5})学院", "XXX学院", totalReplacements)
Call SelectiveReplace("([一-龥]{1,5})学校", "XXX学校", totalReplacements)
Call SelectiveReplace("([一-龥]{1,5})大学", "XXX大学", totalReplacements)
Call SelectiveReplace("([一-龥]{1,5})局", "XXX局", totalReplacements)
Call SelectiveReplace("([一-龥]{1,5})院", "XXX院", totalReplacements)

' -------- 金额脱敏（精确控制版本） --------
' 只使用一种策略，避免重复处理
Call ProcessAmountDesensitization(totalReplacements)

' -------- 人数（只脱敏4位及以下，数字单位之间允许空格） --------
Call SelectiveReplace("([0-9]{1,4})[  ]{0,}人", "XXX人", totalReplacements)
Call SelectiveReplace("([0-9]{1,4})[  ]{0,}名", "XXX名", totalReplacements)
Call SelectiveReplace("([0-9]{1,4})[  ]{0,}位", "XXX位", totalReplacements)

' -------- 面积 / 长度 / 重量 允许空格 --------
Call SelectiveReplace("([0-9]*[0-9.,]*)[  ]{0,}平方米", "XXX平方米", totalReplacements)
Call SelectiveReplace("([0-9]*[0-9.,]*)[  ]{0,}平方公里", "XXX平方公里", totalReplacements)
Call SelectiveReplace("([0-9]*[0-9.,]*)[  ]{0,}公顷", "XXX公顷", totalReplacements)
Call SelectiveReplace("([0-9]*[0-9.,]*)[  ]{0,}亩", "XXX亩", totalReplacements)
Call SelectiveReplace("([0-9]*[0-9.,]*)[  ]{0,}米", "XXX米", totalReplacements)
Call SelectiveReplace("([0-9]*[0-9.,]*)[  ]{0,}公里", "XXX公里", totalReplacements)
Call SelectiveReplace("([0-9]*[0-9.,]*)[  ]{0,}吨", "XXX吨", totalReplacements)
Call SelectiveReplace("([0-9]*[0-9.,]*)[  ]{0,}公斤", "XXX公斤", totalReplacements)

' -------- 百分比 --------
Call SelectiveReplace("([0-9]*[0-9.,]*)[  ]{0,}%", "XX%", totalReplacements)
Call SelectiveReplace("([0-9]*[0-9.,]*)[  ]{0,}％", "XX％", totalReplacements)

' -------- 日期 --------
Call SelectiveReplace("([0-9]{4})年([0-9]{1,2})月([0-9]{1,2})日", "XXXX年XX月XX日", totalReplacements)
Call SelectiveReplace("([0-9]{4})年([0-9]{1,2})月", "XXXX年XX月", totalReplacements)

' -------- 姓名 --------
Dim surnames As Variant: surnames = Array("李", "王", "张", "刘", "陈", "杨", "黄", "赵", "周", "吴", _
    "徐", "孙", "朱", "马", "胡", "郭", "林", "何", "高", "梁")
Dim j As Long
For j = 0 To UBound(surnames)
    Call SelectiveReplace(surnames(j) & "[一-龥]{1,2}", "XXX", totalReplacements)
Next j

' -------- 手动关键词 --------
If MsgBox("是否需要手动指定其他关键词进行脱敏？" & _
    vbCrLf & "（可使用 #关键词、##关键词、###关键词、关键字###、###关键字### 格式）" & _
    vbCrLf & "（新增：*关键词 支持任意字符脱敏）", _
    vbYesNo + vbQuestion, "手动脱敏") = vbYes Then

    Dim userInput As String
    userInput = InputBox("请输入要脱敏的关键词，英文逗号分隔：" & _
        vbCrLf & "【汉字模式】" & _
        vbCrLf & "· 直接输入  关键字   →  仅替换该词" & _
        vbCrLf & "· 输入 #关键字 → 关键词前面1个汉字一起替换" & _
        vbCrLf & "· 输入 ##关键字 → 关键词前面2个汉字一起替换" & _
        vbCrLf & "· 输入 ###关键字 → 关键词前面3个汉字一起替换" & _
        vbCrLf & "· 输入 关键字### → 关键词后面3个汉字一起替换" & _
        vbCrLf & "· 输入 ###关键字### → 关键词前后各3个汉字一起替换" & _
        vbCrLf & "【任意字符模式】" & _
        vbCrLf & "· 输入 *关键字 → 关键词前面1个任意字符一起替换" & _
        vbCrLf & "· 输入 **关键字 → 关键词前面2个任意字符一起替换" & _
        vbCrLf & "· 输入 关键字*** → 关键词后面3个任意字符一起替换" & _
        vbCrLf & "· 输入 **关键字* → 关键词前后任意字符一起替换", _
        "手动脱敏")

    If Len(Trim$(userInput)) > 0 Then
        Dim kwArr() As String: kwArr = Split(userInput, ",")
        Dim k As Long
        For k = LBound(kwArr) To UBound(kwArr)
            Dim token As String: token = Trim$(kwArr(k))
            If Len(token) = 0 Then GoTo NextToken

            ' 判断是使用星号模式还是井号模式
            If InStr(token, "*") > 0 Then
                ' 处理星号模式（任意字符）
                Call HandleWildcardPattern(token, totalReplacements)
            ElseIf InStr(token, "#") > 0 Then
                ' 处理井号模式（汉字）
                Call HandleHashPattern(token, totalReplacements)
            Else
                ' 直接替换
                Call ExactReplace(token, "XXX", totalReplacements)
            End If
NextToken:
        Next k
    End If
End If

Application.ScreenUpdating = True
Dim currentPages As Long: currentPages = doc.ComputeStatistics(wdStatisticPages)
Dim msg As String: msg = "脱敏完成，共处理 " & totalReplacements & " 处。"
If originalPages > currentPages + 1 Then _
    msg = msg & vbCrLf & vbCrLf & "? 页数由 " & originalPages & " 降至 " & currentPages & "，请确认。"
MsgBox msg, vbInformation, "脱敏结果"
End Sub

'-----------------------------------------------------------
' 处理星号通配符模式（任意字符脱敏）
Private Sub HandleWildcardPattern(token As String, ByRef totalReplacements As Long)
On Error GoTo ErrHandler
Dim patt As String, repl As String, core As String
Dim prefixStars As Integer, suffixStars As Integer
Dim i As Integer

' 计算前缀星号数量
prefixStars = 0
For i = 1 To Len(token)
    If Mid(token, i, 1) = "*" Then
        prefixStars = prefixStars + 1
    Else
        Exit For
    End If
Next i

' 计算后缀星号数量
suffixStars = 0
For i = Len(token) To 1 Step -1
    If Mid(token, i, 1) = "*" Then
        suffixStars = suffixStars + 1
    Else
        Exit For
    End If
Next i

' 提取核心关键词（去除星号）
core = Mid(token, prefixStars + 1, Len(token) - prefixStars - suffixStars)

' 构建通配符模式和替换字符串
If prefixStars > 0 And suffixStars > 0 Then
    ' 前后都有星号：**关键字* 或 *关键字***
    patt = RepeatWildcard(prefixStars) & EscapeWildcardChars(core) & RepeatWildcard(suffixStars)
    repl = RepeatX(prefixStars) & core & RepeatX(suffixStars)
    Call SelectiveReplace(patt, repl, totalReplacements)
ElseIf prefixStars > 0 Then
    ' 只有前缀星号：*关键字 或 **关键字
    patt = RepeatWildcard(prefixStars) & EscapeWildcardChars(core)
    repl = RepeatX(prefixStars) & core
    Call SelectiveReplace(patt, repl, totalReplacements)
ElseIf suffixStars > 0 Then
    ' 只有后缀星号：关键字* 或 关键字***
    patt = EscapeWildcardChars(core) & RepeatWildcard(suffixStars)
    repl = core & RepeatX(suffixStars)
    Call SelectiveReplace(patt, repl, totalReplacements)
End If

Exit Sub
ErrHandler:
    Debug.Print "HandleWildcardPattern 错误：" & Err.Description & "；处理：" & token
End Sub

'-----------------------------------------------------------
' 处理井号模式（汉字脱敏）- 保持原有逻辑
Private Sub HandleHashPattern(token As String, ByRef totalReplacements As Long)
On Error GoTo ErrHandler
Dim patt As String, repl As String, core As String
core = Replace(token, "#", "")

If Left(token, 3) = "###" And Right(token, 3) = "###" Then
    patt = "[一-龥][一-龥][一-龥]" & EscapeWildcardChars(core) & "[一-龥][一-龥][一-龥]"
    repl = "XXX" & core & "XXX"
    Call SelectiveReplace(patt, repl, totalReplacements)
ElseIf Left(token, 3) = "###" Then
    patt = "[一-龥][一-龥][一-龥]" & EscapeWildcardChars(core)
    repl = "XXX" & core
    Call SelectiveReplace(patt, repl, totalReplacements)
ElseIf Right(token, 3) = "###" Then
    patt = EscapeWildcardChars(core) & "[一-龥][一-龥][一-龥]"
    repl = core & "XXX"
    Call SelectiveReplace(patt, repl, totalReplacements)
ElseIf Left(token, 2) = "##" Then
    patt = "[一-龥][一-龥]" & EscapeWildcardChars(core)
    repl = "XXX" & core
    Call SelectiveReplace(patt, repl, totalReplacements)
ElseIf Left(token, 1) = "#" Then
    patt = "[一-龥]" & EscapeWildcardChars(core)
    repl = "XXX" & core
    Call SelectiveReplace(patt, repl, totalReplacements)
End If

Exit Sub
ErrHandler:
    Debug.Print "HandleHashPattern 错误：" & Err.Description & "；处理：" & token
End Sub

'-----------------------------------------------------------
' 工具函数：重复生成通配符（?）用于Word通配符搜索
Private Function RepeatWildcard(times As Integer) As String
Dim i As Integer
RepeatWildcard = ""
For i = 1 To times
    RepeatWildcard = RepeatWildcard & "?"
Next i
End Function

'-----------------------------------------------------------
' 工具函数：重复生成X字符
Private Function RepeatX(times As Integer) As String
Dim i As Integer
RepeatX = ""
For i = 1 To times
    RepeatX = RepeatX & "X"
Next i
End Function

'-----------------------------------------------------------
' 工具函数：转义Word通配符搜索的特殊字符
Private Function EscapeWildcardChars(text As String) As String
Dim result As String
result = text
' 转义Word通配符搜索的特殊字符
result = Replace(result, "[", "\[")
result = Replace(result, "]", "\]")
result = Replace(result, "?", "\?")
result = Replace(result, "*", "\*")
result = Replace(result, "<", "\<")
result = Replace(result, ">", "\>")
result = Replace(result, "^", "\^")
result = Replace(result, "{", "\{")
result = Replace(result, "}", "\}")
result = Replace(result, "(", "\(")
result = Replace(result, ")", "\)")
EscapeWildcardChars = result
End Function

'-----------------------------------------------------------
' 选择性替换（支持通配符）
Private Sub SelectiveReplace(findText As String, replaceText As String, ByRef counter As Long)
On Error GoTo ErrHandler
With ActiveDocument.Range.Find
    .ClearFormatting: .Replacement.ClearFormatting
    .text = findText: .Replacement.text = replaceText
    .Forward = True: .Wrap = wdFindContinue
    .Format = False: .MatchWildcards = True
    counter = counter + .Execute(Replace:=wdReplaceAll)
End With
Exit Sub
ErrHandler: Debug.Print "SelectiveReplace 错误：" & Err.Description & "；查找：" & findText
End Sub

'-----------------------------------------------------------
' 精确替换（不使用通配符）
Private Sub ExactReplace(findText As String, replaceText As String, ByRef counter As Long)
On Error GoTo ErrHandler
With ActiveDocument.Range.Find
    .ClearFormatting: .Replacement.ClearFormatting
    .text = findText: .Replacement.text = replaceText
    .Forward = True: .Wrap = wdFindContinue
    .Format = False: .MatchWildcards = False
    counter = counter + .Execute(Replace:=wdReplaceAll)
End With
Exit Sub
ErrHandler: Debug.Print "ExactReplace 错误：" & Err.Description & "；查找：" & findText
End Sub

'-----------------------------------------------------------
' 使用VBA正则表达式处理金额脱敏
Private Sub ProcessAmountDesensitization(ByRef counter As Long)
On Error GoTo ErrHandler
Dim regEx As Object
Set regEx = CreateObject("VBScript.RegExp")
regEx.Global = True
regEx.IgnoreCase = True

' 处理文档主体内容 - 按优先级顺序处理，避免重复替换
Dim rng As Range
Set rng = ActiveDocument.content

' 先处理万元（更具体的模式）
regEx.pattern = "\d+(\.\d+)?\s*万元"
If regEx.Test(rng.text) Then
    rng.text = regEx.Replace(rng.text, "XXX万元")
    counter = counter + 1
End If

' 重新获取内容，处理万（但排除已经处理过的万元）
Set rng = ActiveDocument.content
regEx.pattern = "\d+(\.\d+)?\s*万(?!元)"
If regEx.Test(rng.text) Then
    rng.text = regEx.Replace(rng.text, "XXX万")
    counter = counter + 1
End If

' 处理纯元
Set rng = ActiveDocument.content
regEx.pattern = "\d+(\.\d+)?\s*元(?!$)"
If regEx.Test(rng.text) Then
    rng.text = regEx.Replace(rng.text, "XXX元")
    counter = counter + 1
End If

' 处理表格中的内容
Dim tbl As Table, rw As Row, cell As cell
For Each tbl In ActiveDocument.Tables
    For Each rw In tbl.Rows
        For Each cell In rw.Cells
            Set rng = cell.Range
            rng.End = rng.End - 1

            ' 先处理万元
            regEx.pattern = "\d+(\.\d+)?\s*万元"
            If regEx.Test(rng.text) Then
                rng.text = regEx.Replace(rng.text, "XXX万元")
                counter = counter + 1
            End If

            ' 处理万（排除万元）
            regEx.pattern = "\d+(\.\d+)?\s*万(?!元)"
            If regEx.Test(rng.text) Then
                rng.text = regEx.Replace(rng.text, "XXX万")
                counter = counter + 1
            End If

            ' 处理元
            regEx.pattern = "\d+(\.\d+)?\s*元(?!$)"
            If regEx.Test(rng.text) Then
                rng.text = regEx.Replace(rng.text, "XXX元")
                counter = counter + 1
            End If
        Next cell
    Next rw
Next tbl

Exit Sub
ErrHandler:
    Debug.Print "ProcessAmountDesensitization 错误：" & Err.Description
End Sub