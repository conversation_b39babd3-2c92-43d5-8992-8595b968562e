@echo off
chcp 65001 >nul 2>&1
echo 正在编译Word格式化工具...

REM 查找Visual Studio安装路径
set VS_FOUND=0

echo 正在查找Visual Studio安装...

REM 检查Visual Studio 2022 Community
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    echo 找到Visual Studio 2022 Community
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    set VS_FOUND=1
    goto :compile
)

REM 检查Visual Studio 2022 Professional
if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    echo 找到Visual Studio 2022 Professional
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    set VS_FOUND=1
    goto :compile
)

REM 检查Visual Studio 2022 Enterprise
if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat" (
    echo 找到Visual Studio 2022 Enterprise
    call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    set VS_FOUND=1
    goto :compile
)

REM 检查Visual Studio 2019 Community
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    echo 找到Visual Studio 2019 Community
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    set VS_FOUND=1
    goto :compile
)

REM 检查Visual Studio 2019 Professional
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    echo 找到Visual Studio 2019 Professional
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    set VS_FOUND=1
    goto :compile
)

REM 检查Visual Studio 2019 Enterprise
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\VC\Auxiliary\Build\vcvars64.bat" (
    echo 找到Visual Studio 2019 Enterprise
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    set VS_FOUND=1
    goto :compile
)

REM 尝试使用vswhere工具查找Visual Studio
where vswhere >nul 2>&1
if %errorlevel% == 0 (
    echo 使用vswhere查找Visual Studio...
    for /f "usebackq tokens=*" %%i in (`vswhere -latest -products * -requires Microsoft.VisualStudio.Component.VC.Tools.x86.x64 -property installationPath`) do (
        if exist "%%i\VC\Auxiliary\Build\vcvars64.bat" (
            echo 找到Visual Studio: %%i
            call "%%i\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
            set VS_FOUND=1
            goto :compile
        )
    )
)

REM 如果都没找到，显示错误信息
if %VS_FOUND%==0 (
    echo.
    echo 错误：未找到Visual Studio安装！
    echo.
    echo 请安装以下任一版本：
    echo - Visual Studio 2019 Community/Professional/Enterprise
    echo - Visual Studio 2022 Community/Professional/Enterprise
    echo.
    echo 下载地址：
    echo https://visualstudio.microsoft.com/downloads/
    echo.
    echo 或者尝试安装MinGW-w64并使用g++编译器
    pause
    exit /b 1
)

:compile
echo 开始编译...

REM 检查源文件是否存在
if not exist "WordFormatter_Clean.cpp" (
    echo 错误：找不到源文件 WordFormatter_Clean.cpp
    echo 请确保文件在当前目录中
    pause
    exit /b 1
)

REM 编译命令
cl /EHsc /std:c++17 /utf-8 ^
   /D_WIN32_DCOM /DUNICODE /D_UNICODE ^
   /D_CRT_SECURE_NO_WARNINGS ^
   WordFormatter_Clean.cpp ^
   /link ole32.lib oleaut32.lib uuid.lib user32.lib kernel32.lib ^
   /OUT:WordFormatter.exe

if exist WordFormatter.exe (
    echo.
    echo ========================================
    echo 编译成功！可执行文件: WordFormatter.exe
    echo ========================================
    echo.
    echo 使用方法：
    echo 1. 打开Word文档
    echo 2. 选择需要格式化的文本
    echo 3. 运行 WordFormatter.exe
    echo 4. 按提示输入格式参数
    echo.
    echo 参数格式：编号字体,编号字号,编号粗体,正文字体,正文字号,正文粗体
    echo 示例：宋体,10.5,否,宋体,10.5,否
    echo.
) else (
    echo.
    echo ========================================
    echo 编译失败！
    echo ========================================
    echo.
    echo 可能的问题：
    echo 1. Microsoft Office未安装或版本不兼容
    echo 2. Office安装路径不是默认路径
    echo 3. 缺少必要的开发组件
    echo.
    echo 请检查以下路径是否存在：
    echo C:\Program Files\Microsoft Office\root\Office16\MSWORD.OLB
    echo.
    echo 如果Office版本不同，请修改代码中的路径：
    echo #import "C:\\Program Files\\Microsoft Office\\root\\Office16\\MSWORD.OLB"
    echo.
    echo 常见Office路径：
    echo Office 2016: Office16
    echo Office 2019: Office16
    echo Office 2021: Office16
    echo Office 365: Office16
)

pause