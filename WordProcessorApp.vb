Imports Microsoft.Office.Interop.Word
Imports System.Text.RegularExpressions
Imports System.IO
Imports System.Windows.Forms

Public Class WordProcessorApp
    Inherits Form

    Private wordApp As Microsoft.Office.Interop.Word.Application
    Private currentDoc As Document
    
    ' UI Controls
    Private WithEvents btnDesensitize As Button
    Private WithEvents btnFormatTable As Button
    Private WithEvents btnAddThousandSeparator As Button
    Private WithEvents btnConvertToPDF As Button
    Private WithEvents btnRemoveSpaces As Button
    Private WithEvents btnNumbering As Button
    Private lblStatus As Label
    Private progressBar As ProgressBar
    
    Public Sub New()
        InitializeComponent()
        InitializeWordApp()
    End Sub
    
    Private Sub InitializeComponent()
        Me.Text = "Word文档处理工具 VB.NET版 v1.0"
        Me.Size = New Size(900, 700)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.FormBorderStyle = FormBorderStyle.FixedSingle
        Me.MaximizeBox = False
        
        ' 创建分组框
        Dim grpDesensitize As New GroupBox With {
            .Text = "文档脱敏",
            .Location = New Point(20, 20),
            .Size = New Size(200, 120)
        }
        
        Dim grpFormat As New GroupBox With {
            .Text = "格式处理",
            .Location = New Point(240, 20),
            .Size = New Size(200, 120)
        }
        
        Dim grpConvert As New GroupBox With {
            .Text = "转换工具",
            .Location = New Point(460, 20),
            .Size = New Size(200, 120)
        }
        
        Dim grpText As New GroupBox With {
            .Text = "文本处理",
            .Location = New Point(680, 20),
            .Size = New Size(180, 120)
        }
        
        ' 脱敏功能按钮
        btnDesensitize = New Button With {
            .Text = "一键脱敏",
            .Size = New Size(80, 30),
            .Location = New Point(10, 25),
            .BackColor = Color.LightCoral,
            .ForeColor = Color.White
        }
        
        Dim btnCustomDesensitize As New Button With {
            .Text = "自定义脱敏",
            .Size = New Size(80, 30),
            .Location = New Point(100, 25)
        }
        
        ' 格式处理按钮
        btnFormatTable = New Button With {
            .Text = "表格格式化",
            .Size = New Size(80, 30),
            .Location = New Point(10, 25),
            .BackColor = Color.LightBlue
        }
        
        btnNumbering = New Button With {
            .Text = "自动编号",
            .Size = New Size(80, 30),
            .Location = New Point(100, 25)
        }
        
        Dim btnParagraphFormat As New Button With {
            .Text = "段落格式",
            .Size = New Size(80, 30),
            .Location = New Point(10, 65)
        }
        
        ' 转换工具按钮
        btnConvertToPDF = New Button With {
            .Text = "批量转PDF",
            .Size = New Size(80, 30),
            .Location = New Point(10, 25),
            .BackColor = Color.LightGreen
        }
        
        btnAddThousandSeparator = New Button With {
            .Text = "千分位符",
            .Size = New Size(80, 30),
            .Location = New Point(100, 25)
        }
        
        ' 文本处理按钮
        btnRemoveSpaces = New Button With {
            .Text = "去除空白",
            .Size = New Size(70, 30),
            .Location = New Point(10, 25)
        }
        
        Dim btnReplaceText As New Button With {
            .Text = "文本替换",
            .Size = New Size(70, 30),
            .Location = New Point(90, 25)
        }
        
        ' 添加按钮到分组框
        grpDesensitize.Controls.AddRange({btnDesensitize, btnCustomDesensitize})
        grpFormat.Controls.AddRange({btnFormatTable, btnNumbering, btnParagraphFormat})
        grpConvert.Controls.AddRange({btnConvertToPDF, btnAddThousandSeparator})
        grpText.Controls.AddRange({btnRemoveSpaces, btnReplaceText})
        
        ' 创建日志文本框
        Dim lblLog As New Label With {
            .Text = "操作日志:",
            .Location = New Point(20, 160),
            .Size = New Size(100, 20)
        }
        
        Dim txtLog As New TextBox With {
            .Location = New Point(20, 185),
            .Size = New Size(840, 200),
            .Multiline = True,
            .ScrollBars = ScrollBars.Vertical,
            .ReadOnly = True,
            .BackColor = Color.Black,
            .ForeColor = Color.LimeGreen,
            .Font = New Font("Consolas", 9)
        }
        
        ' 状态栏
        lblStatus = New Label With {
            .Text = "就绪 - 请先在Word中打开文档",
            .Location = New Point(20, 400),
            .Size = New Size(500, 20),
            .ForeColor = Color.Blue
        }
        
        ' 进度条
        progressBar = New ProgressBar With {
            .Location = New Point(20, 430),
            .Size = New Size(840, 25),
            .Visible = False,
            .Style = ProgressBarStyle.Continuous
        }
        
        ' 版本信息和帮助
        Dim lblVersion As New Label With {
            .Text = "版本 1.0.0 | 基于VB.NET开发",
            .Location = New Point(20, 470),
            .Size = New Size(300, 20),
            .ForeColor = Color.Gray
        }
        
        Dim btnHelp As New Button With {
            .Text = "使用帮助",
            .Size = New Size(80, 30),
            .Location = New Point(700, 465)
        }
        
        Dim btnAbout As New Button With {
            .Text = "关于",
            .Size = New Size(60, 30),
            .Location = New Point(790, 465)
        }
        
        ' 添加所有控件到窗体
        Me.Controls.AddRange({grpDesensitize, grpFormat, grpConvert, grpText,
                             lblLog, txtLog, lblStatus, progressBar, 
                             lblVersion, btnHelp, btnAbout})
        
        ' 存储日志文本框的引用
        Me.Tag = txtLog
    End Sub
    
    Private Sub InitializeWordApp()
        Try
            wordApp = New Microsoft.Office.Interop.Word.Application()
            wordApp.Visible = True
            lblStatus.Text = "Word应用程序已连接"
            LogMessage("Word应用程序连接成功")
        Catch ex As Exception
            lblStatus.Text = "Word连接失败"
            LogMessage($"错误: 无法连接到Word应用程序 - {ex.Message}")
            MessageBox.Show($"无法连接到Word应用程序: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' 日志记录方法
    Private Sub LogMessage(message As String)
        Try
            Dim txtLog As TextBox = CType(Me.Tag, TextBox)
            If txtLog IsNot Nothing Then
                Dim timestamp As String = DateTime.Now.ToString("HH:mm:ss")
                txtLog.AppendText($"[{timestamp}] {message}{vbCrLf}")
                txtLog.SelectionStart = txtLog.Text.Length
                txtLog.ScrollToCaret()
            End If
        Catch ex As Exception
            ' 忽略日志记录错误
        End Try
    End Sub
    
    ' 文档脱敏功能
    Private Sub btnDesensitize_Click(sender As Object, e As EventArgs) Handles btnDesensitize.Click
        Try
            If wordApp Is Nothing OrElse wordApp.ActiveDocument Is Nothing Then
                LogMessage("错误: 未找到活动的Word文档")
                MessageBox.Show("请先打开一个Word文档", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If
            
            currentDoc = wordApp.ActiveDocument
            LogMessage($"开始脱敏文档: {currentDoc.Name}")
            lblStatus.Text = "正在进行文档脱敏..."
            progressBar.Visible = True
            progressBar.Value = 0
            
            Dim totalReplacements As Integer = 0
            
            ' 机构名脱敏
            LogMessage("正在处理机构名脱敏...")
            progressBar.Value = 10
            Dim institutionCount = ProcessInstitutionDesensitization()
            totalReplacements += institutionCount
            LogMessage($"机构名脱敏完成，处理 {institutionCount} 处")
            
            ' 金额脱敏
            LogMessage("正在处理金额脱敏...")
            progressBar.Value = 30
            Dim amountCount = ProcessAmountDesensitization()
            totalReplacements += amountCount
            LogMessage($"金额脱敏完成，处理 {amountCount} 处")
            
            ' 地名脱敏
            LogMessage("正在处理地名脱敏...")
            progressBar.Value = 50
            Dim locationCount = ProcessLocationDesensitization()
            totalReplacements += locationCount
            LogMessage($"地名脱敏完成，处理 {locationCount} 处")
            
            ' 姓名脱敏
            LogMessage("正在处理姓名脱敏...")
            progressBar.Value = 70
            Dim nameCount = ProcessNameDesensitization()
            totalReplacements += nameCount
            LogMessage($"姓名脱敏完成，处理 {nameCount} 处")
            
            ' 日期脱敏
            LogMessage("正在处理日期脱敏...")
            progressBar.Value = 90
            Dim dateCount = ProcessDateDesensitization()
            totalReplacements += dateCount
            LogMessage($"日期脱敏完成，处理 {dateCount} 处")
            
            progressBar.Value = 100
            progressBar.Visible = False
            lblStatus.Text = $"脱敏完成，共处理 {totalReplacements} 处"
            LogMessage($"文档脱敏全部完成！总计处理 {totalReplacements} 处敏感信息")
            
            MessageBox.Show($"文档脱敏完成！\n共处理 {totalReplacements} 处敏感信息", "完成", MessageBoxButtons.OK, MessageBoxIcon.Information)
            
        Catch ex As Exception
            progressBar.Visible = False
            lblStatus.Text = "脱敏过程中出现错误"
            LogMessage($"脱敏过程出错: {ex.Message}")
            MessageBox.Show($"脱敏过程中出现错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' 机构名脱敏
    Private Function ProcessInstitutionDesensitization() As Integer
        Dim count As Integer = 0
        Dim patterns() As String = {
            "([一-龥]{6})([一-龥]{0,20})公司",
            "([一-龥]{6})([一-龥]{0,20})有限公司",
            "([一-龥]{6})([一-龥]{0,20})股份有限公司",
            "([一-龥]{1,5})公司",
            "([一-龥]{1,5})有限公司",
            "([一-龥]{1,5})学院",
            "([一-龥]{1,5})大学"
        }
        
        Dim replacements() As String = {
            "XXXXXX$2公司",
            "XXXXXX$2有限公司", 
            "XXXXXX$2股份有限公司",
            "XXX公司",
            "XXX有限公司",
            "XXX学院",
            "XXX大学"
        }
        
        For i As Integer = 0 To patterns.Length - 1
            count += ReplaceWithRegex(patterns(i), replacements(i))
        Next
        
        Return count
    End Function
    
    ' 金额脱敏
    Private Function ProcessAmountDesensitization() As Integer
        Dim count As Integer = 0
        
        ' 使用.NET正则表达式处理金额
        Dim content As String = currentDoc.Content.Text
        
        ' 万元模式
        Dim regex1 As New Regex("\d+(\.\d+)?\s*万元", RegexOptions.IgnoreCase)
        Dim matches1 = regex1.Matches(content)
        For Each match As Match In matches1
            currentDoc.Content.Find.Execute(FindText:=match.Value, ReplaceWith:="XXX万元", Replace:=WdReplace.wdReplaceAll)
            count += 1
        Next
        
        ' 万模式（排除万元）
        Dim regex2 As New Regex("\d+(\.\d+)?\s*万(?!元)", RegexOptions.IgnoreCase)
        Dim matches2 = regex2.Matches(content)
        For Each match As Match In matches2
            currentDoc.Content.Find.Execute(FindText:=match.Value, ReplaceWith:="XXX万", Replace:=WdReplace.wdReplaceAll)
            count += 1
        Next
        
        ' 元模式
        Dim regex3 As New Regex("\d+(\.\d+)?\s*元", RegexOptions.IgnoreCase)
        Dim matches3 = regex3.Matches(content)
        For Each match As Match In matches3
            If Not match.Value.Contains("万元") Then
                currentDoc.Content.Find.Execute(FindText:=match.Value, ReplaceWith:="XXX元", Replace:=WdReplace.wdReplaceAll)
                count += 1
            End If
        Next
        
        Return count
    End Function
    
    ' 地名脱敏
    Private Function ProcessLocationDesensitization() As Integer
        Dim count As Integer = 0
        Dim suffixes() As String = {"路", "街", "大道", "街道", "省", "市", "县", "区", "镇", "乡", "村"}
        
        For Each suffix As String In suffixes
            Dim pattern As String = $"[\u4e00-\u9fa5]{{2,6}}{suffix}"
            Dim regex As New Regex(pattern)
            Dim content As String = currentDoc.Content.Text
            Dim matches = regex.Matches(content)
            
            For Each match As Match In matches
                currentDoc.Content.Find.Execute(FindText:=match.Value, ReplaceWith:=$"XXX{suffix}", Replace:=WdReplace.wdReplaceAll)
                count += 1
            Next
        Next
        
        Return count
    End Function
    
    ' 姓名脱敏
    Private Function ProcessNameDesensitization() As Integer
        Dim count As Integer = 0
        Dim surnames() As String = {"李", "王", "张", "刘", "陈", "杨", "黄", "赵", "周", "吴", "徐", "孙", "朱", "马", "胡", "郭", "林", "何", "高", "梁"}
        
        For Each surname As String In surnames
            Dim pattern As String = $"{surname}[一-龥]{{1,2}}"
            Dim regex As New Regex(pattern)
            Dim content As String = currentDoc.Content.Text
            Dim matches = regex.Matches(content)
            
            For Each match As Match In matches
                currentDoc.Content.Find.Execute(FindText:=match.Value, ReplaceWith:="XXX", Replace:=WdReplace.wdReplaceAll)
                count += 1
            Next
        Next
        
        Return count
    End Function
    
    ' 日期脱敏
    Private Function ProcessDateDesensitization() As Integer
        Dim count As Integer = 0
        
        ' 年月日格式
        count += ReplaceWithRegex("([0-9]{4})年([0-9]{1,2})月([0-9]{1,2})日", "XXXX年XX月XX日")
        
        ' 年月格式
        count += ReplaceWithRegex("([0-9]{4})年([0-9]{1,2})月", "XXXX年XX月")
        
        Return count
    End Function
    
    ' 正则表达式替换辅助方法
    Private Function ReplaceWithRegex(pattern As String, replacement As String) As Integer
        Try
            Dim findRange As Range = currentDoc.Content
            With findRange.Find
                .ClearFormatting()
                .Replacement.ClearFormatting()
                .Text = pattern
                .Replacement.Text = replacement
                .Forward = True
                .Wrap = WdFindWrap.wdFindContinue
                .Format = False
                .MatchWildcards = True
                Return .Execute(Replace:=WdReplace.wdReplaceAll)
            End With
        Catch ex As Exception
            Return 0
        End Try
    End Function
    
    ' 表格格式化功能
    Private Sub btnFormatTable_Click(sender As Object, e As EventArgs) Handles btnFormatTable.Click
        Try
            If wordApp Is Nothing OrElse wordApp.ActiveDocument Is Nothing Then
                MessageBox.Show("请先打开一个Word文档", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If
            
            currentDoc = wordApp.ActiveDocument
            lblStatus.Text = "正在格式化表格..."
            
            Dim tableCount As Integer = 0
            For Each table As Table In currentDoc.Tables
                FormatSingleTable(table)
                tableCount += 1
            Next
            
            lblStatus.Text = $"表格格式化完成，共处理 {tableCount} 个表格"
            MessageBox.Show($"表格格式化完成！\n共处理 {tableCount} 个表格", "完成", MessageBoxButtons.OK, MessageBoxIcon.Information)
            
        Catch ex As Exception
            lblStatus.Text = "表格格式化过程中出现错误"
            MessageBox.Show($"表格格式化过程中出现错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' 格式化单个表格
    Private Sub FormatSingleTable(table As Table)
        With table
            ' 设置单元格边距
            .TopPadding = wordApp.PixelsToPoints(2, True)
            .BottomPadding = wordApp.PixelsToPoints(2, True)
            .LeftPadding = wordApp.PixelsToPoints(7, True)
            .RightPadding = wordApp.PixelsToPoints(10, True)
            
            ' 设置字体
            With .Range.Font
                .NameFarEast = "宋体"
                .NameAscii = "Times New Roman"
                .Size = 11
            End With
            
            ' 设置行高
            For i As Integer = 1 To .Rows.Count
                .Rows(i).HeightRule = WdRowHeightRule.wdRowHeightAtLeast
                .Rows(i).Height = wordApp.CentimetersToPoints(0.7)
            Next
            
            ' 设置边框
            With .Borders(WdBorderType.wdBorderTop)
                .LineStyle = WdLineStyle.wdLineStyleDouble
                .LineWidth = WdLineWidth.wdLineWidth050pt
            End With
            
            With .Borders(WdBorderType.wdBorderBottom)
                .LineStyle = WdLineStyle.wdLineStyleDouble
                .LineWidth = WdLineWidth.wdLineWidth050pt
            End With
            
            With .Borders(WdBorderType.wdBorderHorizontal)
                .LineStyle = WdLineStyle.wdLineStyleSingle
                .LineWidth = WdLineWidth.wdLineWidth050pt
            End With
            
            With .Borders(WdBorderType.wdBorderVertical)
                .LineStyle = WdLineStyle.wdLineStyleSingle
                .LineWidth = WdLineWidth.wdLineWidth050pt
            End With
            
            ' 自动调整表格宽度
            .AutoFitBehavior(WdAutoFitBehavior.wdAutoFitWindow)
        End With
    End Sub
    
    ' 添加千分位分隔符
    Private Sub btnAddThousandSeparator_Click(sender As Object, e As EventArgs) Handles btnAddThousandSeparator.Click
        Try
            If wordApp Is Nothing OrElse wordApp.Selection Is Nothing Then
                MessageBox.Show("请先选择要处理的内容", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If
            
            lblStatus.Text = "正在添加千分位分隔符..."
            
            Dim selection = wordApp.Selection
            Dim count As Integer = 0
            
            ' 处理选中的数字
            If selection.Type = WdSelectionType.wdSelectionNormal Then
                Dim text As String = selection.Text
                If IsNumeric(text) Then
                    Dim number As Double = Convert.ToDouble(text)
                    selection.Text = number.ToString("#,##0.00")
                    count = 1
                End If
            ElseIf selection.Type = WdSelectionType.wdSelectionRow OrElse selection.Type = WdSelectionType.wdSelectionColumn Then
                ' 处理表格中的数字
                For Each cell As Cell In selection.Cells
                    Dim cellText As String = cell.Range.Text.Replace(Chr(13), "").Replace(Chr(7), "")
                    If IsNumeric(cellText) Then
                        Dim number As Double = Convert.ToDouble(cellText)
                        cell.Range.Text = number.ToString("#,##0.00")
                        count += 1
                    End If
                Next
            End If
            
            lblStatus.Text = $"千分位添加完成，共处理 {count} 个数字"
            MessageBox.Show($"千分位分隔符添加完成！\n共处理 {count} 个数字", "完成", MessageBoxButtons.OK, MessageBoxIcon.Information)
            
        Catch ex As Exception
            lblStatus.Text = "添加千分位过程中出现错误"
            MessageBox.Show($"添加千分位过程中出现错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' 批量转换PDF
    Private Sub btnConvertToPDF_Click(sender As Object, e As EventArgs) Handles btnConvertToPDF.Click
        Try
            Dim folderDialog As New FolderBrowserDialog()
            folderDialog.Description = "选择包含Word文档的文件夹"
            
            If folderDialog.ShowDialog() = DialogResult.OK Then
                lblStatus.Text = "正在批量转换PDF..."
                progressBar.Visible = True
                
                Dim folderPath As String = folderDialog.SelectedPath
                Dim wordFiles() As String = Directory.GetFiles(folderPath, "*.doc*")
                
                progressBar.Maximum = wordFiles.Length
                progressBar.Value = 0
                
                Dim convertedCount As Integer = 0
                
                For Each filePath As String In wordFiles
                    Try
                        Dim doc As Document = wordApp.Documents.Open(filePath, ReadOnly:=True)
                        Dim pdfPath As String = Path.ChangeExtension(filePath, ".pdf")
                        
                        doc.ExportAsFixedFormat(pdfPath, WdExportFormat.wdExportFormatPDF)
                        doc.Close(SaveChanges:=False)
                        
                        convertedCount += 1
                        progressBar.Value += 1
                        lblStatus.Text = $"正在转换: {Path.GetFileName(filePath)}"
                        System.Windows.Forms.Application.DoEvents()
                        
                    Catch ex As Exception
                        ' 跳过有问题的文件，继续处理下一个
                        Continue For
                    End Try
                Next
                
                progressBar.Visible = False
                lblStatus.Text = $"PDF转换完成，共转换 {convertedCount} 个文件"
                MessageBox.Show($"PDF转换完成！\n共转换 {convertedCount} 个文件", "完成", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
            
        Catch ex As Exception
            progressBar.Visible = False
            lblStatus.Text = "PDF转换过程中出现错误"
            MessageBox.Show($"PDF转换过程中出现错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' 去除空白
    Private Sub btnRemoveSpaces_Click(sender As Object, e As EventArgs) Handles btnRemoveSpaces.Click
        Try
            If wordApp Is Nothing OrElse wordApp.Selection Is Nothing Then
                MessageBox.Show("请先选择要处理的内容", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If
            
            lblStatus.Text = "正在去除空白..."
            
            Dim selection = wordApp.Selection
            Dim originalText As String = selection.Text
            Dim newText As String = originalText
            
            ' 删除各种空格
            newText = newText.Replace(" ", "")  ' 普通空格
            newText = newText.Replace("　", "") ' 全角空格
            newText = newText.Replace(Chr(160), "") ' 不间断空格
            newText = newText.Replace(Chr(9), "")   ' 制表符
            
            ' 处理连续回车
            Do
                Dim oldText As String = newText
                newText = newText.Replace(vbCrLf & vbCrLf, vbCrLf)
                newText = newText.Replace(vbCr & vbCr, vbCr)
            Loop Until oldText = newText
            
            If newText <> originalText Then
                selection.Text = newText
                lblStatus.Text = "空白去除完成"
                MessageBox.Show("空白字符去除完成！", "完成", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Else
                lblStatus.Text = "未发现需要去除的空白字符"
                MessageBox.Show("未发现需要去除的空白字符", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            End If
            
        Catch ex As Exception
            lblStatus.Text = "去除空白过程中出现错误"
            MessageBox.Show($"去除空白过程中出现错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' 自动编号
    Private Sub btnNumbering_Click(sender As Object, e As EventArgs) Handles btnNumbering.Click
        Try
            If wordApp Is Nothing OrElse wordApp.Selection Is Nothing Then
                MessageBox.Show("请先选择要编号的段落", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If
            
            ' 弹出编号类型选择对话框
            Dim result As DialogResult = MessageBox.Show("选择编号类型：" & vbCrLf & 
                                                        "是 - 中文数字编号（一、二、三）" & vbCrLf & 
                                                        "否 - 阿拉伯数字编号（1、2、3）" & vbCrLf & 
                                                        "取消 - 取消操作", 
                                                        "选择编号类型", 
                                                        MessageBoxButtons.YesNoCancel, 
                                                        MessageBoxIcon.Question)
            
            If result = DialogResult.Cancel Then Return
            
            lblStatus.Text = "正在应用自动编号..."
            
            Dim selection = wordApp.Selection
            Dim listTemplate As ListTemplate = wordApp.ListGalleries(WdListGalleryType.wdNumberGallery).ListTemplates(1)
            
            If result = DialogResult.Yes Then
                ' 中文数字编号
                With listTemplate.ListLevels(1)
                    .NumberFormat = "%1、"
                    .NumberStyle = WdListNumberStyle.wdListNumberStyleSimpChinNum1
                    .Font.Name = "宋体"
                    .Font.Size = 11
                End With
            Else
                ' 阿拉伯数字编号
                With listTemplate.ListLevels(1)
                    .NumberFormat = "%1、"
                    .NumberStyle = WdListNumberStyle.wdListNumberStyleArabic
                    .Font.Name = "宋体"
                    .Font.Size = 11
                End With
            End If
            
            selection.Range.ListFormat.ApplyListTemplateWithLevel(listTemplate, True, WdListApplyTo.wdListApplyToWholeList)
            
            lblStatus.Text = "自动编号应用完成"
            MessageBox.Show("自动编号应用完成！", "完成", MessageBoxButtons.OK, MessageBoxIcon.Information)
            
        Catch ex As Exception
            lblStatus.Text = "自动编号过程中出现错误"
            MessageBox.Show($"自动编号过程中出现错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' 窗体关闭时清理资源
    Protected Overrides Sub OnFormClosed(e As FormClosedEventArgs)
        Try
            If wordApp IsNot Nothing Then
                ' 不关闭Word应用程序，只是断开连接
                wordApp = Nothing
            End If
        Catch ex As Exception
            ' 忽略清理时的错误
        End Try
        MyBase.OnFormClosed(e)
    End Sub
    
End Class

' 程序入口点
Module Program
    <STAThread>
    Sub Main()
        System.Windows.Forms.Application.EnableVisualStyles()
        System.Windows.Forms.Application.SetCompatibleTextRenderingDefault(False)
        System.Windows.Forms.Application.Run(New WordProcessorApp())
    End Sub
End Module