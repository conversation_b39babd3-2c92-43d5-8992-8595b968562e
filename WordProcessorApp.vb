Imports System.Windows.Forms
Imports System.Drawing
Imports WordApp = Microsoft.Office.Interop.Word.Application
Imports WordDocument = Microsoft.Office.Interop.Word.Document
Imports WordRange = Microsoft.Office.Interop.Word.Range
Imports WordTable = Microsoft.Office.Interop.Word.Table
Imports WordCell = Microsoft.Office.Interop.Word.Cell

Public Class WordProcessorMainForm
    Inherits Form
    
    Private WithEvents btnUnit10000 As Button
    Private WithEvents btnGetSum As Button
    Private WithEvents btnFormatTable As Button
    Private WithEvents btnDelBlank As Button
    Private WithEvents btnQianfen As Button
    Private WithEvents btnConvertToPDF As Button
    
    Public Sub New()
        InitializeComponent()
    End Sub
    
    Private Sub InitializeComponent()
        Me.Text = "Word文档处理工具"
        Me.Size = New Size(600, 500)
        Me.StartPosition = FormStartPosition.CenterScreen
        
        ' 创建按钮
        btnUnit10000 = New Button With {
            .Text = "金额-万位到元",
            .Size = New Size(120, 40),
            .Location = New Point(20, 20)
        }
        
        btnGetSum = New Button With {
            .Text = "表格求和",
            .Size = New Size(120, 40),
            .Location = New Point(160, 20)
        }
        
        btnFormatTable = New Button With {
            .Text = "格式化表格",
            .Size = New Size(120, 40),
            .Location = New Point(300, 20)
        }
        
        btnDelBlank = New Button With {
            .Text = "去除空白",
            .Size = New Size(120, 40),
            .Location = New Point(440, 20)
        }
        
        btnQianfen = New Button With {
            .Text = "千分位符",
            .Size = New Size(120, 40),
            .Location = New Point(20, 80)
        }
        
        btnConvertToPDF = New Button With {
            .Text = "批量转PDF",
            .Size = New Size(120, 40),
            .Location = New Point(160, 80)
        }
        
        ' 添加控件到窗体
        Me.Controls.AddRange({btnUnit10000, btnGetSum, btnFormatTable, btnDelBlank, btnQianfen, btnConvertToPDF})
    End Sub
    
    Private Sub btnUnit10000_Click(sender As Object, e As EventArgs) Handles btnUnit10000.Click
        Try
            Unit10000()
        Catch ex As Exception
            MessageBox.Show($"操作失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub btnGetSum_Click(sender As Object, e As EventArgs) Handles btnGetSum.Click
        Try
            GetSum()
        Catch ex As Exception
            MessageBox.Show($"操作失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub btnFormatTable_Click(sender As Object, e As EventArgs) Handles btnFormatTable.Click
        Try
            FormatSingleTable()
        Catch ex As Exception
            MessageBox.Show($"操作失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub btnDelBlank_Click(sender As Object, e As EventArgs) Handles btnDelBlank.Click
        Try
            DelBlank()
        Catch ex As Exception
            MessageBox.Show($"操作失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub btnQianfen_Click(sender As Object, e As EventArgs) Handles btnQianfen.Click
        Try
            Qianfen()
        Catch ex As Exception
            MessageBox.Show($"操作失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    Private Sub btnConvertToPDF_Click(sender As Object, e As EventArgs) Handles btnConvertToPDF.Click
        Try
            ConvertToPDF()
        Catch ex As Exception
            MessageBox.Show($"操作失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' 核心功能方法
    Private Sub Unit10000()
        Dim wordApp As WordApp = Nothing
        Try
            wordApp = GetWordApplication()
            If wordApp Is Nothing Then
                Return
            End If
            
            Dim selection = wordApp.Selection
            Dim selectedText As String = selection.Text?.Trim()
            
            If String.IsNullOrEmpty(selectedText) Then
                MessageBox.Show("请先选择要转换的数字", "提示")
                Return
            End If
            
            If IsNumeric(selectedText) Then
                Dim p As Double = CDbl(selectedText)
                Dim q As String = Format(Math.Round(p, 2), "#,##0.00")
                selection.Text = q & "万"
                MessageBox.Show("转换完成", "成功")
            Else
                MessageBox.Show($"选中的内容不是有效数字：{selectedText}", "提示")
            End If
            
        Catch ex As Exception
            Throw New Exception($"金额转换失败：{ex.Message}")
        End Try
    End Sub
    
    Private Sub GetSum()
        Dim wordApp As WordApp = Nothing
        Try
            wordApp = GetWordApplication()
            If wordApp Is Nothing Then
                Return
            End If
            
            Dim selection = wordApp.Selection
            Dim total As Double = 0
            Dim cellCount As Integer = 0
            
            If selection.Cells.Count = 0 Then
                MessageBox.Show("请选择表格单元格", "提示")
                Return
            End If
            
            For Each cell As WordCell In selection.Cells
                Dim cellText As String = cell.Range.Text.Replace(Chr(13), "").Replace(Chr(7), "").Trim()
                If IsNumeric(cellText) Then
                    total += CDbl(cellText)
                    cellCount += 1
                End If
            Next
            
            If cellCount = 0 Then
                MessageBox.Show("选中的单元格中没有找到数字", "提示")
            Else
                MessageBox.Show($"合计：{Format(total, "#,##0.00")}" & vbCrLf & $"共计算了 {cellCount} 个数字", "求和结果")
            End If
            
        Catch ex As Exception
            Throw New Exception($"求和失败：{ex.Message}")
        End Try
    End Sub
    
    Private Sub FormatSingleTable()
        Dim wordApp As WordApp = Nothing
        Try
            wordApp = GetWordApplication()
            If wordApp Is Nothing Then
                MessageBox.Show("请先打开Word文档", "提示")
                Return
            End If
            
            Dim selection = wordApp.Selection
            If Not selection.Information(Microsoft.Office.Interop.Word.WdInformation.wdWithInTable) Then
                MessageBox.Show("请将光标放在表格中", "提示")
                Return
            End If
            
            Dim table As WordTable = selection.Tables(1)
            
            ' 设置表格格式
            With table
                .AutoFitBehavior(Microsoft.Office.Interop.Word.WdAutoFitBehavior.wdAutoFitWindow)
                .Range.Font.NameFarEast = "宋体"
                .Range.Font.NameAscii = "Times New Roman"
                .Range.Font.Size = 11
            End With
            
            ' 设置边框
            With table.Borders
                .Item(Microsoft.Office.Interop.Word.WdBorderType.wdBorderTop).LineStyle = Microsoft.Office.Interop.Word.WdLineStyle.wdLineStyleDouble
                .Item(Microsoft.Office.Interop.Word.WdBorderType.wdBorderBottom).LineStyle = Microsoft.Office.Interop.Word.WdLineStyle.wdLineStyleDouble
                .Item(Microsoft.Office.Interop.Word.WdBorderType.wdBorderLeft).LineStyle = Microsoft.Office.Interop.Word.WdLineStyle.wdLineStyleNone
                .Item(Microsoft.Office.Interop.Word.WdBorderType.wdBorderRight).LineStyle = Microsoft.Office.Interop.Word.WdLineStyle.wdLineStyleNone
                .Item(Microsoft.Office.Interop.Word.WdBorderType.wdBorderHorizontal).LineStyle = Microsoft.Office.Interop.Word.WdLineStyle.wdLineStyleSingle
                .Item(Microsoft.Office.Interop.Word.WdBorderType.wdBorderVertical).LineStyle = Microsoft.Office.Interop.Word.WdLineStyle.wdLineStyleSingle
            End With
            
            MessageBox.Show("表格格式化完成", "成功")
            
        Catch ex As Exception
            Throw New Exception($"表格格式化失败：{ex.Message}")
        End Try
    End Sub
    
    Private Sub DelBlank()
        Dim wordApp As WordApp = Nothing
        Try
            wordApp = GetWordApplication()
            If wordApp Is Nothing Then
                MessageBox.Show("请先打开Word文档", "提示")
                Return
            End If
            
            Dim selection = wordApp.Selection
            If selection.Type = Microsoft.Office.Interop.Word.WdSelectionType.wdSelectionIP Then
                MessageBox.Show("请选择要处理的文本", "提示")
                Return
            End If
            
            Dim selectedText As String = selection.Text
            
            ' 去除空格和多余换行
            selectedText = selectedText.Replace(" ", "")
            selectedText = selectedText.Replace("　", "")
            selectedText = selectedText.Replace(Chr(160), "")
            selectedText = selectedText.Replace(Chr(9), "")
            
            ' 处理多余换行
            Dim oldText As String
            Do
                oldText = selectedText
                selectedText = selectedText.Replace(vbCr & vbCr, vbCr)
            Loop Until oldText = selectedText
            
            selection.Text = selectedText
            MessageBox.Show("空白清理完成", "成功")
            
        Catch ex As Exception
            Throw New Exception($"空白清理失败：{ex.Message}")
        End Try
    End Sub
    
    Private Sub Qianfen()
        Dim wordApp As WordApp = Nothing
        Try
            wordApp = GetWordApplication()
            If wordApp Is Nothing Then
                MessageBox.Show("请先打开Word文档", "提示")
                Return
            End If
            
            Dim selection = wordApp.Selection
            
            If selection.Type = Microsoft.Office.Interop.Word.WdSelectionType.wdSelectionNormal Then
                If IsNumeric(selection.Text) Then
                    Dim number As Double = CDbl(selection.Text)
                    Dim formatted As String = Format(number, "#,##0.00")
                    selection.Text = formatted
                    MessageBox.Show("千分位格式化完成", "成功")
                Else
                    MessageBox.Show("请选择数字内容", "提示")
                End If
            Else
                MessageBox.Show("请选择文本或表格单元格", "提示")
            End If
            
        Catch ex As Exception
            Throw New Exception($"千分位格式化失败：{ex.Message}")
        End Try
    End Sub
    
    Private Sub ConvertToPDF()
        Try
            Dim folderDialog As New FolderBrowserDialog()
            folderDialog.Description = "选择要转换的文件夹"
            
            If folderDialog.ShowDialog() = DialogResult.OK Then
                Dim folderPath As String = folderDialog.SelectedPath
                Dim wordFiles() As String = System.IO.Directory.GetFiles(folderPath, "*.doc*")
                
                If wordFiles.Length = 0 Then
                    MessageBox.Show("未找到Word文件", "提示")
                    Return
                End If
                
                Dim wordApp As New WordApp()
                wordApp.Visible = False
                
                Dim convertedCount As Integer = 0
                
                For Each filePath As String In wordFiles
                    Try
                        Dim doc As WordDocument = wordApp.Documents.Open(filePath, ReadOnly:=True)
                        Dim pdfPath As String = System.IO.Path.ChangeExtension(filePath, ".pdf")
                        
                        doc.ExportAsFixedFormat(pdfPath, Microsoft.Office.Interop.Word.WdExportFormat.wdExportFormatPDF)
                        doc.Close(False)
                        convertedCount += 1
                        
                    Catch ex As Exception
                        Continue For
                    End Try
                Next
                
                wordApp.Quit(False)
                MessageBox.Show($"转换完成，共处理 {convertedCount} 个文件", "成功")
            End If
            
        Catch ex As Exception
            Throw New Exception($"PDF转换失败：{ex.Message}")
        End Try
    End Sub
    
    Private Function GetWordApplication() As WordApp
        Try
            ' 首先尝试连接到已运行的Word实例
            Dim wordApp As WordApp = Nothing
            Try
                wordApp = CType(GetObject(, "Word.Application"), WordApp)
                ' 检查是否有活动文档
                If wordApp.Documents.Count > 0 Then
                    Return wordApp
                End If
            Catch
                ' 如果没有运行的Word实例，尝试创建新的
            End Try
            
            ' 创建新的Word实例
            Try
                wordApp = CType(CreateObject("Word.Application"), WordApp)
                wordApp.Visible = True
                Return wordApp
            Catch ex As Exception
                Throw New Exception($"无法启动Word应用程序：{ex.Message}")
            End Try
            
        Catch ex As Exception
            Throw New Exception($"连接Word失败：{ex.Message}")
        End Try
    End Function
End Class

Module Program
    <STAThread>
    Sub Main()
        System.Windows.Forms.Application.EnableVisualStyles()
        System.Windows.Forms.Application.SetCompatibleTextRenderingDefault(False)
        System.Windows.Forms.Application.Run(New WordProcessorMainForm())
    End Sub
End Module