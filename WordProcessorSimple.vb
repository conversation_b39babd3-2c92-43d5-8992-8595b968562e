Imports System.Windows.Forms
Imports System.Drawing

Public Class SimpleWordProcessorForm
    Inherits Form
    
    Private WithEvents btnTest As Button
    Private WithEvents btnUnit10000 As Button
    Private WithEvents btnGetSum As Button
    Private lblStatus As Label
    
    Public Sub New()
        InitializeComponent()
    End Sub
    
    Private Sub InitializeComponent()
        Me.Text = "Word文档处理工具 - 简化版"
        Me.Size = New Size(500, 300)
        Me.StartPosition = FormStartPosition.CenterScreen
        
        ' 状态标签
        lblStatus = New Label With {
            .Text = "状态：等待操作",
            .Size = New Size(450, 30),
            .Location = New Point(20, 20),
            .ForeColor = Color.Blue
        }
        
        ' 测试连接按钮
        btnTest = New Button With {
            .Text = "测试Word连接",
            .Size = New Size(120, 40),
            .Location = New Point(20, 60)
        }
        
        ' 金额转换按钮
        btnUnit10000 = New Button With {
            .Text = "金额-万位到元",
            .Size = New Size(120, 40),
            .Location = New Point(160, 60)
        }
        
        ' 表格求和按钮
        btnGetSum = New Button With {
            .Text = "表格求和",
            .Size = New Size(120, 40),
            .Location = New Point(300, 60)
        }
        
        ' 添加控件到窗体
        Me.Controls.AddRange({lblStatus, btnTest, btnUnit10000, btnGetSum})
    End Sub
    
    Private Sub btnTest_Click(sender As Object, e As EventArgs) Handles btnTest.Click
        Try
            lblStatus.Text = "状态：正在测试Word连接..."
            lblStatus.ForeColor = Color.Orange
            Application.DoEvents()
            
            Dim wordApp As Object = Nothing
            
            ' 尝试连接到现有Word实例
            Try
                wordApp = GetObject(, "Word.Application")
                If wordApp IsNot Nothing Then
                    lblStatus.Text = $"状态：已连接到Word，文档数量：{wordApp.Documents.Count}"
                    lblStatus.ForeColor = Color.Green
                    
                    If wordApp.Documents.Count = 0 Then
                        MessageBox.Show("Word已启动但没有打开文档。请在Word中打开一个文档后再试。", "提示")
                    End If
                Else
                    lblStatus.Text = "状态：无法获取Word实例"
                    lblStatus.ForeColor = Color.Red
                End If
            Catch ex As Exception
                ' 如果连接失败，尝试启动Word
                Try
                    lblStatus.Text = "状态：Word未运行，正在启动..."
                    lblStatus.ForeColor = Color.Orange
                    Application.DoEvents()
                    
                    wordApp = CreateObject("Word.Application")
                    wordApp.Visible = True
                    
                    lblStatus.Text = "状态：Word已启动，请打开文档后再使用功能"
                    lblStatus.ForeColor = Color.Blue
                    MessageBox.Show("Word已启动，请打开一个文档后再使用其他功能。", "提示")
                    
                Catch ex2 As Exception
                    lblStatus.Text = $"状态：无法启动Word - {ex2.Message}"
                    lblStatus.ForeColor = Color.Red
                    MessageBox.Show($"无法连接或启动Word：{ex2.Message}" & vbCrLf & vbCrLf & "请确保：" & vbCrLf & "1. 已安装Microsoft Word" & vbCrLf & "2. Word没有被其他程序占用", "错误")
                End Try
            End Try
            
        Catch ex As Exception
            lblStatus.Text = $"状态：测试失败 - {ex.Message}"
            lblStatus.ForeColor = Color.Red
        End Try
    End Sub
    
    Private Sub btnUnit10000_Click(sender As Object, e As EventArgs) Handles btnUnit10000.Click
        Try
            lblStatus.Text = "状态：正在处理金额转换..."
            lblStatus.ForeColor = Color.Orange
            Application.DoEvents()
            
            Dim wordApp As Object = Nothing
            
            ' 尝试连接到Word
            Try
                wordApp = GetObject(, "Word.Application")
            Catch ex As Exception
                MessageBox.Show("无法连接到Word。请先启动Word并打开文档。", "错误")
                lblStatus.Text = "状态：Word连接失败"
                lblStatus.ForeColor = Color.Red
                Return
            End Try
            
            If wordApp Is Nothing Then
                MessageBox.Show("Word应用程序未找到", "错误")
                lblStatus.Text = "状态：Word未找到"
                lblStatus.ForeColor = Color.Red
                Return
            End If
            
            If wordApp.Documents.Count = 0 Then
                MessageBox.Show("请先在Word中打开文档", "提示")
                lblStatus.Text = "状态：没有打开的Word文档"
                lblStatus.ForeColor = Color.Red
                Return
            End If
            
            Dim selection = wordApp.Selection
            Dim selectedText As String = ""
            
            Try
                selectedText = selection.Text
            Catch ex As Exception
                MessageBox.Show("无法获取Word中的选中内容", "错误")
                lblStatus.Text = "状态：无法获取选中内容"
                lblStatus.ForeColor = Color.Red
                Return
            End Try
            
            If String.IsNullOrEmpty(selectedText?.Trim()) Then
                MessageBox.Show("请先在Word中选择要转换的数字", "提示")
                lblStatus.Text = "状态：没有选中文本"
                lblStatus.ForeColor = Color.Red
                Return
            End If
            
            selectedText = selectedText.Trim()
            
            If IsNumeric(selectedText) Then
                Dim number As Double = CDbl(selectedText)
                Dim formatted As String = Format(Math.Round(number, 2), "#,##0.00")
                
                Try
                    selection.Text = formatted & "万"
                    MessageBox.Show($"转换完成：{selectedText} → {formatted}万", "成功")
                    lblStatus.Text = "状态：金额转换完成"
                    lblStatus.ForeColor = Color.Green
                Catch ex As Exception
                    MessageBox.Show($"无法修改Word文档内容：{ex.Message}", "错误")
                    lblStatus.Text = "状态：无法修改文档"
                    lblStatus.ForeColor = Color.Red
                End Try
            Else
                MessageBox.Show($"选中的内容不是有效数字：{selectedText}", "错误")
                lblStatus.Text = "状态：选中内容不是数字"
                lblStatus.ForeColor = Color.Red
            End If
            
        Catch ex As Exception
            MessageBox.Show($"操作失败：{ex.Message}", "错误")
            lblStatus.Text = $"状态：操作失败 - {ex.Message}"
            lblStatus.ForeColor = Color.Red
        End Try
    End Sub
    
    Private Sub btnGetSum_Click(sender As Object, e As EventArgs) Handles btnGetSum.Click
        Try
            lblStatus.Text = "状态：正在计算表格求和..."
            lblStatus.ForeColor = Color.Orange
            Application.DoEvents()
            
            Dim wordApp As Object = GetObject(, "Word.Application")
            
            If wordApp.Documents.Count = 0 Then
                MessageBox.Show("请先在Word中打开文档", "提示")
                lblStatus.Text = "状态：没有打开的Word文档"
                lblStatus.ForeColor = Color.Red
                Return
            End If
            
            Dim selection = wordApp.Selection
            
            If selection.Cells.Count = 0 Then
                MessageBox.Show("请在Word中选择表格单元格", "提示")
                lblStatus.Text = "状态：没有选中表格单元格"
                lblStatus.ForeColor = Color.Red
                Return
            End If
            
            Dim total As Double = 0
            Dim cellCount As Integer = 0
            
            For Each cell In selection.Cells
                Dim cellText As String = cell.Range.Text
                ' 清理单元格文本
                cellText = cellText.Replace(Chr(13), "").Replace(Chr(7), "").Trim()
                
                If IsNumeric(cellText) Then
                    total += CDbl(cellText)
                    cellCount += 1
                End If
            Next
            
            If cellCount = 0 Then
                MessageBox.Show("选中的单元格中没有找到数字", "提示")
                lblStatus.Text = "状态：没有找到数字"
                lblStatus.ForeColor = Color.Red
            Else
                Dim result As String = $"合计：{Format(total, "#,##0.00")}" & vbCrLf & $"共计算了 {cellCount} 个数字"
                MessageBox.Show(result, "求和结果")
                lblStatus.Text = $"状态：求和完成，合计：{Format(total, "#,##0.00")}"
                lblStatus.ForeColor = Color.Green
            End If
            
        Catch ex As Exception
            MessageBox.Show($"操作失败：{ex.Message}", "错误")
            lblStatus.Text = $"状态：操作失败 - {ex.Message}"
            lblStatus.ForeColor = Color.Red
        End Try
    End Sub
End Class

Module SimpleProgram
    <STAThread>
    Sub Main()
        System.Windows.Forms.Application.EnableVisualStyles()
        System.Windows.Forms.Application.SetCompatibleTextRenderingDefault(False)
        System.Windows.Forms.Application.Run(New SimpleWordProcessorForm())
    End Sub
End Module