Imports System.Text.RegularExpressions
Imports System.IO
Imports System.Windows.Forms
Imports System.Drawing
Imports Word = Microsoft.Office.Interop.Word

Public Class WordProcessorApp
    Inherits Form

    Private wordApp As Word.Application
    Private currentDoc As Word.Document
    
    ' UI Controls
    Private WithEvents btnDesensitize As Button
    Private WithEvents btnFormatTable As Button
    Private WithEvents btnAddThousandSeparator As Button
    Private WithEvents btnConvertToPDF As Button
    Private WithEvents btnRemoveSpaces As Button
    Private WithEvents btnNumbering As Button
    Private lblStatus As Label
    Private progressBar As ProgressBar
    Private txtLog As TextBox
    
    Public Sub New()
        InitializeComponent()
        InitializeWordApp()
    End Sub
    
    Private Sub InitializeComponent()
        Me.Text = "Word文档处理工具 VB.NET版 v1.0"
        Me.Size = New Size(900, 700)
        Me.StartPosition = FormStartPosition.CenterScreen
        Me.FormBorderStyle = FormBorderStyle.FixedSingle
        Me.MaximizeBox = False
        
        ' 创建分组框
        Dim grpDesensitize As New GroupBox With {
            .Text = "文档脱敏",
            .Location = New Point(20, 20),
            .Size = New Size(200, 120)
        }
        
        Dim grpFormat As New GroupBox With {
            .Text = "格式处理",
            .Location = New Point(240, 20),
            .Size = New Size(200, 120)
        }
        
        Dim grpConvert As New GroupBox With {
            .Text = "转换工具",
            .Location = New Point(460, 20),
            .Size = New Size(200, 120)
        }
        
        Dim grpText As New GroupBox With {
            .Text = "文本处理",
            .Location = New Point(680, 20),
            .Size = New Size(180, 120)
        }
        
        ' 脱敏功能按钮
        btnDesensitize = New Button With {
            .Text = "一键脱敏",
            .Size = New Size(80, 30),
            .Location = New Point(10, 25),
            .BackColor = Color.LightCoral,
            .ForeColor = Color.White
        }
        
        Dim btnCustomDesensitize As New Button With {
            .Text = "自定义脱敏",
            .Size = New Size(80, 30),
            .Location = New Point(100, 25)
        }
        
        ' 格式处理按钮
        btnFormatTable = New Button With {
            .Text = "表格格式化",
            .Size = New Size(80, 30),
            .Location = New Point(10, 25),
            .BackColor = Color.LightBlue
        }
        
        btnNumbering = New Button With {
            .Text = "自动编号",
            .Size = New Size(80, 30),
            .Location = New Point(100, 25)
        }
        
        Dim btnParagraphFormat As New Button With {
            .Text = "段落格式",
            .Size = New Size(80, 30),
            .Location = New Point(10, 65)
        }
        
        ' 转换工具按钮
        btnConvertToPDF = New Button With {
            .Text = "批量转PDF",
            .Size = New Size(80, 30),
            .Location = New Point(10, 25),
            .BackColor = Color.LightGreen
        }
        
        btnAddThousandSeparator = New Button With {
            .Text = "千分位符",
            .Size = New Size(80, 30),
            .Location = New Point(100, 25)
        }
        
        ' 文本处理按钮
        btnRemoveSpaces = New Button With {
            .Text = "去除空白",
            .Size = New Size(70, 30),
            .Location = New Point(10, 25)
        }
        
        Dim btnReplaceText As New Button With {
            .Text = "文本替换",
            .Size = New Size(70, 30),
            .Location = New Point(90, 25)
        }
        
        ' 添加按钮到分组框
        grpDesensitize.Controls.AddRange({btnDesensitize, btnCustomDesensitize})
        grpFormat.Controls.AddRange({btnFormatTable, btnNumbering, btnParagraphFormat})
        grpConvert.Controls.AddRange({btnConvertToPDF, btnAddThousandSeparator})
        grpText.Controls.AddRange({btnRemoveSpaces, btnReplaceText})
        
        ' 创建日志文本框
        Dim lblLog As New Label With {
            .Text = "操作日志:",
            .Location = New Point(20, 160),
            .Size = New Size(100, 20)
        }
        
        txtLog = New TextBox With {
            .Location = New Point(20, 185),
            .Size = New Size(840, 200),
            .Multiline = True,
            .ScrollBars = ScrollBars.Vertical,
            .ReadOnly = True,
            .BackColor = Color.Black,
            .ForeColor = Color.LimeGreen,
            .Font = New Font("Consolas", 9)
        }
        
        ' 状态栏
        lblStatus = New Label With {
            .Text = "就绪 - 请先在Word中打开文档",
            .Location = New Point(20, 400),
            .Size = New Size(500, 20),
            .ForeColor = Color.Blue
        }
        
        ' 进度条
        progressBar = New ProgressBar With {
            .Location = New Point(20, 430),
            .Size = New Size(840, 25),
            .Visible = False,
            .Style = ProgressBarStyle.Continuous
        }
        
        ' 版本信息和帮助
        Dim lblVersion As New Label With {
            .Text = "版本 1.0.0 | 基于VB.NET开发",
            .Location = New Point(20, 470),
            .Size = New Size(300, 20),
            .ForeColor = Color.Gray
        }
        
        Dim btnHelp As New Button With {
            .Text = "使用帮助",
            .Size = New Size(80, 30),
            .Location = New Point(700, 465)
        }
        
        Dim btnAbout As New Button With {
            .Text = "关于",
            .Size = New Size(60, 30),
            .Location = New Point(790, 465)
        }
        
        ' 添加所有控件到窗体
        Me.Controls.AddRange({grpDesensitize, grpFormat, grpConvert, grpText,
                             lblLog, txtLog, lblStatus, progressBar, 
                             lblVersion, btnHelp, btnAbout})
    End Sub
    
    Private Sub InitializeWordApp()
        Try
            wordApp = New Word.Application()
            wordApp.Visible = True
            lblStatus.Text = "Word应用程序已连接"
            LogMessage("Word应用程序连接成功")
        Catch ex As Exception
            lblStatus.Text = "Word连接失败"
            LogMessage($"错误: 无法连接到Word应用程序 - {ex.Message}")
            MessageBox.Show($"无法连接到Word应用程序: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' 日志记录方法
    Private Sub LogMessage(message As String)
        Try
            If txtLog IsNot Nothing Then
                Dim timestamp As String = DateTime.Now.ToString("HH:mm:ss")
                txtLog.AppendText($"[{timestamp}] {message}{vbCrLf}")
                txtLog.SelectionStart = txtLog.Text.Length
                txtLog.ScrollToCaret()
            End If
        Catch ex As Exception
            ' 忽略日志记录错误
        End Try
    End Sub
    
    ' 文档脱敏功能
    Private Sub btnDesensitize_Click(sender As Object, e As EventArgs) Handles btnDesensitize.Click
        Try
            If wordApp Is Nothing OrElse wordApp.ActiveDocument Is Nothing Then
                LogMessage("错误: 未找到活动的Word文档")
                MessageBox.Show("请先打开一个Word文档", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If
            
            currentDoc = wordApp.ActiveDocument
            LogMessage($"开始脱敏文档: {currentDoc.Name}")
            lblStatus.Text = "正在进行文档脱敏..."
            progressBar.Visible = True
            progressBar.Value = 0
            
            Dim totalReplacements As Integer = 0
            
            ' 机构名脱敏
            LogMessage("正在处理机构名脱敏...")
            progressBar.Value = 20
            Dim institutionCount = ProcessInstitutionDesensitization()
            totalReplacements += institutionCount
            LogMessage($"机构名脱敏完成，处理 {institutionCount} 处")
            
            ' 金额脱敏
            LogMessage("正在处理金额脱敏...")
            progressBar.Value = 40
            Dim amountCount = ProcessAmountDesensitization()
            totalReplacements += amountCount
            LogMessage($"金额脱敏完成，处理 {amountCount} 处")
            
            ' 地名脱敏
            LogMessage("正在处理地名脱敏...")
            progressBar.Value = 60
            Dim locationCount = ProcessLocationDesensitization()
            totalReplacements += locationCount
            LogMessage($"地名脱敏完成，处理 {locationCount} 处")
            
            ' 姓名脱敏
            LogMessage("正在处理姓名脱敏...")
            progressBar.Value = 80
            Dim nameCount = ProcessNameDesensitization()
            totalReplacements += nameCount
            LogMessage($"姓名脱敏完成，处理 {nameCount} 处")
            
            ' 日期脱敏
            LogMessage("正在处理日期脱敏...")
            progressBar.Value = 100
            Dim dateCount = ProcessDateDesensitization()
            totalReplacements += dateCount
            LogMessage($"日期脱敏完成，处理 {dateCount} 处")
            
            progressBar.Visible = False
            lblStatus.Text = $"脱敏完成，共处理 {totalReplacements} 处"
            LogMessage($"文档脱敏全部完成！总计处理 {totalReplacements} 处敏感信息")
            
            MessageBox.Show($"文档脱敏完成！{vbCrLf}共处理 {totalReplacements} 处敏感信息", "完成", MessageBoxButtons.OK, MessageBoxIcon.Information)
            
        Catch ex As Exception
            progressBar.Visible = False
            lblStatus.Text = "脱敏过程中出现错误"
            LogMessage($"脱敏过程出错: {ex.Message}")
            MessageBox.Show($"脱敏过程中出现错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' 机构名脱敏
    Private Function ProcessInstitutionDesensitization() As Integer
        Dim count As Integer = 0
        Try
            ' 使用Word的查找替换功能
            With currentDoc.Content.Find
                .ClearFormatting()
                .Replacement.ClearFormatting()
                .MatchWildcards = True
                
                ' 处理公司名称
                .Text = "([一-龥]{1,10})公司"
                .Replacement.Text = "XXX公司"
                count += .Execute(Replace:=Word.WdReplace.wdReplaceAll)
                
                ' 处理有限公司
                .Text = "([一-龥]{1,10})有限公司"
                .Replacement.Text = "XXX有限公司"
                count += .Execute(Replace:=Word.WdReplace.wdReplaceAll)
                
                ' 处理学院
                .Text = "([一-龥]{1,10})学院"
                .Replacement.Text = "XXX学院"
                count += .Execute(Replace:=Word.WdReplace.wdReplaceAll)
                
                ' 处理大学
                .Text = "([一-龥]{1,10})大学"
                .Replacement.Text = "XXX大学"
                count += .Execute(Replace:=Word.WdReplace.wdReplaceAll)
            End With
        Catch ex As Exception
            LogMessage($"机构名脱敏出错: {ex.Message}")
        End Try
        Return count
    End Function
    
    ' 金额脱敏
    Private Function ProcessAmountDesensitization() As Integer
        Dim count As Integer = 0
        Try
            With currentDoc.Content.Find
                .ClearFormatting()
                .Replacement.ClearFormatting()
                .MatchWildcards = True
                
                ' 处理万元
                .Text = "[0-9]*万元"
                .Replacement.Text = "XXX万元"
                count += .Execute(Replace:=Word.WdReplace.wdReplaceAll)
                
                ' 处理万
                .Text = "[0-9]*万"
                .Replacement.Text = "XXX万"
                count += .Execute(Replace:=Word.WdReplace.wdReplaceAll)
                
                ' 处理元
                .Text = "[0-9]*元"
                .Replacement.Text = "XXX元"
                count += .Execute(Replace:=Word.WdReplace.wdReplaceAll)
            End With
        Catch ex As Exception
            LogMessage($"金额脱敏出错: {ex.Message}")
        End Try
        Return count
    End Function
    
    ' 地名脱敏
    Private Function ProcessLocationDesensitization() As Integer
        Dim count As Integer = 0
        Try
            Dim suffixes() As String = {"路", "街", "大道", "省", "市", "县", "区"}
            
            With currentDoc.Content.Find
                .ClearFormatting()
                .Replacement.ClearFormatting()
                .MatchWildcards = True
                
                For Each suffix As String In suffixes
                    .Text = $"[一-龥]*{suffix}"
                    .Replacement.Text = $"XXX{suffix}"
                    count += .Execute(Replace:=Word.WdReplace.wdReplaceAll)
                Next
            End With
        Catch ex As Exception
            LogMessage($"地名脱敏出错: {ex.Message}")
        End Try
        Return count
    End Function
    
    ' 姓名脱敏
    Private Function ProcessNameDesensitization() As Integer
        Dim count As Integer = 0
        Try
            Dim surnames() As String = {"李", "王", "张", "刘", "陈", "杨", "黄", "赵", "周", "吴"}
            
            With currentDoc.Content.Find
                .ClearFormatting()
                .Replacement.ClearFormatting()
                .MatchWildcards = True
                
                For Each surname As String In surnames
                    .Text = $"{surname}[一-龥][一-龥]"
                    .Replacement.Text = "XXX"
                    count += .Execute(Replace:=Word.WdReplace.wdReplaceAll)
                    
                    .Text = $"{surname}[一-龥]"
                    .Replacement.Text = "XXX"
                    count += .Execute(Replace:=Word.WdReplace.wdReplaceAll)
                Next
            End With
        Catch ex As Exception
            LogMessage($"姓名脱敏出错: {ex.Message}")
        End Try
        Return count
    End Function
    
    ' 日期脱敏
    Private Function ProcessDateDesensitization() As Integer
        Dim count As Integer = 0
        Try
            With currentDoc.Content.Find
                .ClearFormatting()
                .Replacement.ClearFormatting()
                .MatchWildcards = True
                
                ' 年月日格式
                .Text = "[0-9][0-9][0-9][0-9]年[0-9]*月[0-9]*日"
                .Replacement.Text = "XXXX年XX月XX日"
                count += .Execute(Replace:=Word.WdReplace.wdReplaceAll)
                
                ' 年月格式
                .Text = "[0-9][0-9][0-9][0-9]年[0-9]*月"
                .Replacement.Text = "XXXX年XX月"
                count += .Execute(Replace:=Word.WdReplace.wdReplaceAll)
            End With
        Catch ex As Exception
            LogMessage($"日期脱敏出错: {ex.Message}")
        End Try
        Return count
    End Function
    
    ' 表格格式化功能
    Private Sub btnFormatTable_Click(sender As Object, e As EventArgs) Handles btnFormatTable.Click
        Try
            If wordApp Is Nothing OrElse wordApp.ActiveDocument Is Nothing Then
                MessageBox.Show("请先打开一个Word文档", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If
            
            currentDoc = wordApp.ActiveDocument
            LogMessage("开始格式化表格...")
            lblStatus.Text = "正在格式化表格..."
            
            Dim tableCount As Integer = 0
            For Each table As Word.Table In currentDoc.Tables
                FormatSingleTable(table)
                tableCount += 1
            Next
            
            lblStatus.Text = $"表格格式化完成，共处理 {tableCount} 个表格"
            LogMessage($"表格格式化完成，共处理 {tableCount} 个表格")
            MessageBox.Show($"表格格式化完成！{vbCrLf}共处理 {tableCount} 个表格", "完成", MessageBoxButtons.OK, MessageBoxIcon.Information)
            
        Catch ex As Exception
            lblStatus.Text = "表格格式化过程中出现错误"
            LogMessage($"表格格式化出错: {ex.Message}")
            MessageBox.Show($"表格格式化过程中出现错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
    
    ' 格式化单个表格
    Private Sub FormatSingleTable(table As Word.Table)
        Try
            With table
                ' 设置字体
                With .Range.Font
                    .NameFarEast = "宋体"
                    .NameAscii = "Times New Roman"
                    .Size = 11
                End With
                
                ' 设置边框
                With .Borders(Word.WdBorderType.wdBorderTop)
                    .LineStyle = Word.WdLineStyle.wdLineStyleDouble
                    .LineWidth = Word.WdLineWidth.wdLineWidth050pt
                End With
                
                With .Borders(Word.WdBorderType.wdBorderBottom)
                    .LineStyle = Word.WdLineStyle.wdLineStyleDouble
                    .LineWidth = Word.WdLineWidth.wdLineWidth050pt
                End With
                
                ' 自动调整表格宽度
                .AutoFitBehavior(Word.WdAutoFitBehavior.wdAutoFitWindow)
            End With
        Catch ex As Exception
            LogMessage($"格式化表格出错: {ex.Message}")
        End Try
    End Sub
    
    ' 其他按钮事件处理程序（简化版本）
    Private Sub btnAddThousandSeparator_Click(sender As Object, e As EventArgs) Handles btnAddThousandSeparator.Click
        MessageBox.Show("千分位符功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub btnConvertToPDF_Click(sender As Object, e As EventArgs) Handles btnConvertToPDF.Click
        MessageBox.Show("PDF转换功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub btnRemoveSpaces_Click(sender As Object, e As EventArgs) Handles btnRemoveSpaces.Click
        MessageBox.Show("去除空白功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    Private Sub btnNumbering_Click(sender As Object, e As EventArgs) Handles btnNumbering.Click
        MessageBox.Show("自动编号功能开发中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub
    
    ' 窗体关闭时清理资源
    Protected Overrides Sub OnFormClosed(e As FormClosedEventArgs)
        Try
            If wordApp IsNot Nothing Then
                wordApp = Nothing
            End If
        Catch ex As Exception
            ' 忽略清理时的错误
        End Try
        MyBase.OnFormClosed(e)
    End Sub
    
End Class

' 程序入口点
Module Program
    <STAThread>
    Sub Main()
        System.Windows.Forms.Application.EnableVisualStyles()
        System.Windows.Forms.Application.SetCompatibleTextRenderingDefault(False)
        System.Windows.Forms.Application.Run(New WordProcessorApp())
    End Sub
End Module