# AI客户端 - C++版本

这是从VBA代码转换而来的C++ AI客户端，支持调用多个AI大模型API。

## 功能特性

- 支持OpenAI、<PERSON>、通义千问三个AI模型
- 使用Windows注册表存储API密钥
- 简洁的控制台界面
- 完整的错误处理
- HTTP/HTTPS请求支持

## 编译要求

- Windows 10/11
- Visual Studio 2019或更高版本
- CMake 3.10或更高版本

## 编译方法

### 方法一：使用批处理文件（推荐）
```bash
build.bat
```

### 方法二：手动编译
```bash
mkdir build
cd build
cmake .. -G "Visual Studio 16 2019" -A x64
cmake --build . --config Release
```

## 使用方法

1. **运行程序**
   ```bash
   AIClient.exe
   ```

2. **配置API密钥**
   - 选择菜单选项 "2. 配置API"
   - 按提示输入相应的API密钥

3. **调用AI**
   - 选择菜单选项 "1. 调用AI"
   - 输入您的问题
   - 查看AI回复

## API密钥获取

### OpenAI
- 网址：https://platform.openai.com/api-keys
- 格式：sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

### Claude
- 网址：https://console.anthropic.com/
- 格式：sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

### 通义千问
- 网址：https://dashscope.console.aliyun.com/
- 格式：sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

## 项目结构

```
├── AIClient.h          # 主类头文件
├── AIClient.cpp        # 主类实现
├── main.cpp           # 程序入口
├── CMakeLists.txt     # CMake配置文件
├── build.bat          # 编译脚本
└── README.md          # 说明文档
```

## 主要类和方法

### AIClient类
- `LoadAPISettings()` - 加载API配置
- `SaveAPISettings()` - 保存API配置
- `ConfigureAPI()` - 配置API密钥
- `CallAI(prompt)` - 调用所有启用的AI
- `CallSingleAPI(config, prompt)` - 调用单个AI
- `SendHTTPRequest()` - 发送HTTP请求

### 数据结构
- `APIConfig` - API配置信息
- `AIResponse` - AI响应结果

## 从VBA转换的主要变化

1. **数据类型**：String → std::string
2. **数组**：VBA数组 → std::vector
3. **HTTP请求**：MSXML2.XMLHTTP → WinINet API
4. **注册表操作**：VBA函数 → Windows Registry API
5. **错误处理**：On Error → try-catch
6. **UI界面**：MsgBox/InputBox → 控制台输入输出

## 扩展功能

可以进一步扩展的功能：
- GUI界面（使用Qt或Win32 API）
- 配置文件支持（JSON/XML）
- 更多AI模型支持
- 异步请求处理
- 日志记录功能

## 注意事项

- 需要网络连接才能调用AI API
- API密钥存储在Windows注册表中
- 支持HTTPS请求
- 默认超时时间为30秒

## 故障排除

1. **编译错误**：确保安装了Visual Studio和CMake
2. **网络错误**：检查网络连接和防火墙设置
3. **API错误**：验证API密钥是否正确
4. **编码问题**：程序已设置UTF-8编码支持中文