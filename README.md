# Word文档处理工具 VB.NET版

这是一个基于VB.NET开发的Word文档批量处理工具，将原有的VBA宏功能转换为独立的Windows应用程序。

## 功能特性

### 1. 文档脱敏
- **机构名脱敏**: 自动识别并脱敏公司、学院、大学等机构名称
- **金额脱敏**: 处理各种金额格式（万元、元等）
- **地名脱敏**: 脱敏路名、街道、省市县等地理位置信息
- **姓名脱敏**: 基于常见姓氏的姓名脱敏
- **日期脱敏**: 年月日格式的日期脱敏

### 2. 表格格式化
- 统一表格样式和字体
- 自动调整行高和列宽
- 设置标准边框样式
- 优化单元格对齐方式

### 3. 数字处理
- 为数字添加千分位分隔符
- 支持选中文本和表格单元格处理
- 自动识别数字格式

### 4. 批量转换PDF
- 选择文件夹批量转换Word文档为PDF
- 支持.doc和.docx格式
- 显示转换进度

### 5. 文本处理
- 去除多余空白字符
- 删除全角/半角空格
- 清理连续回车符

### 6. 自动编号
- 中文数字编号（一、二、三）
- 阿拉伯数字编号（1、2、3）
- 自动应用编号格式

## 系统要求

- Windows 10/11
- .NET 6.0 Runtime
- Microsoft Office Word 2016或更高版本
- Visual Studio 2022（开发环境）

## 安装步骤

### 方法1：编译源代码
1. 安装Visual Studio 2022
2. 安装.NET 6.0 SDK
3. 克隆或下载项目源代码
4. 打开`WordProcessor.vbproj`
5. 还原NuGet包：
   ```
   dotnet restore
   ```
6. 编译项目：
   ```
   dotnet build --configuration Release
   ```
7. 运行程序：
   ```
   dotnet run
   ```

### 方法2：发布为独立可执行文件
```bash
# 发布为单文件可执行程序
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true

# 发布为依赖框架的程序（需要安装.NET Runtime）
dotnet publish -c Release -r win-x64 --self-contained false
```

## 使用方法

1. **启动程序**: 双击运行编译后的exe文件
2. **打开Word**: 程序会自动连接到Word应用程序
3. **打开文档**: 在Word中打开需要处理的文档
4. **选择功能**: 点击相应按钮执行所需功能
5. **查看结果**: 程序会显示处理进度和结果

## 功能说明

### 文档脱敏使用步骤
1. 在Word中打开需要脱敏的文档
2. 点击"文档脱敏"按钮
3. 程序会自动处理各类敏感信息
4. 完成后显示处理统计信息

### 表格格式化使用步骤
1. 打开包含表格的Word文档
2. 点击"表格格式化"按钮
3. 程序会自动格式化文档中的所有表格

### 批量转PDF使用步骤
1. 点击"批量转PDF"按钮
2. 选择包含Word文档的文件夹
3. 程序会自动转换所有Word文档为PDF

## 注意事项

1. **备份文档**: 处理前请备份重要文档
2. **Word版本**: 确保安装了兼容的Microsoft Office版本
3. **权限要求**: 程序需要读写文档的权限
4. **内存使用**: 处理大文档时可能需要较多内存
5. **COM组件**: 程序依赖Word的COM接口，确保Word正常安装

## 故障排除

### 常见问题

**Q: 程序提示"无法连接到Word应用程序"**
A: 确保Microsoft Word已正确安装，并且没有被其他程序占用

**Q: 脱敏功能不完整**
A: 检查文档格式，某些特殊格式可能需要手动处理

**Q: 表格格式化失败**
A: 确保表格没有被保护，并且格式正确

**Q: PDF转换失败**
A: 检查Word文档是否损坏，确保有足够的磁盘空间

### 性能优化建议

1. 处理大文档时关闭不必要的程序
2. 确保有足够的内存和磁盘空间
3. 避免同时处理多个大文档
4. 定期清理临时文件

## 开发信息

- **开发语言**: VB.NET
- **框架版本**: .NET 6.0
- **UI框架**: Windows Forms
- **Office版本**: Microsoft Office Interop
- **正则表达式**: System.Text.RegularExpressions

## 版本历史

- **v1.0.0**: 初始版本，包含基本的文档处理功能
  - 文档脱敏
  - 表格格式化
  - 数字处理
  - 批量PDF转换
  - 文本清理
  - 自动编号

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请联系开发者。