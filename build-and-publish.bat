@echo off
echo ========================================
echo Word文档处理工具 编译和发布脚本
echo ========================================
echo.

echo 正在编译项目...
dotnet build WordProcessorApp.vbproj -c Release

if errorlevel 1 (
    echo.
    echo 编译失败
    pause
    exit /b 1
)

echo.
echo 正在发布独立可执行文件...
dotnet publish WordProcessorApp.vbproj -c Release -r win-x64 -p:PublishSingleFile=true -p:SelfContained=true -o publish

if errorlevel 1 (
    echo.
    echo 发布失败，尝试不使用SingleFile...
    dotnet publish WordProcessorApp.vbproj -c Release -r win-x64 -p:SelfContained=true -o publish-multi
    if errorlevel 1 (
        echo 发布仍然失败
        pause
        exit /b 1
    ) else (
        echo.
        echo 发布成功！（多文件版本）
        echo 文件位置: publish-multi\WordProcessorApp.exe
    )
) else (
    echo.
    echo 发布成功！（单文件版本）
    echo 文件位置: publish\WordProcessorApp.exe
)

echo.
echo 程序功能说明：
echo - 金额万位到元：将选中的数字转换为万元格式
echo - 表格求和：计算选中表格单元格的数值总和
echo - 格式化表格：美化Word表格的格式和边框
echo - 去除空白：清理文档中的多余空格和换行
echo - 千分位符：为数字添加千分位逗号分隔符
echo - 批量转PDF：将文件夹中的Word文档批量转换为PDF
echo.
pause