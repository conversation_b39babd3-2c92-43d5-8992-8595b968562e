<Project Sdk="Microsoft.NET.Sdk">

  <!-- ================== 项目属性 ================== -->
  <PropertyGroup>
    <!-- 生成 Windows GUI 可执行文件 -->
    <OutputType>WinExe</OutputType>

    <!-- 目标框架：带 -windows 后缀，自动引入 WinForms & Drawing -->
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>

    <!-- 语法糖：隐式导入常用命名空间 / 可空检查 -->
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <!-- 元数据 -->
    <RootNamespace>WordProcessor</RootNamespace>
    <AssemblyName>WordProcessor</AssemblyName>
    <StartupObject>WordProcessor.Program</StartupObject>

    <AssemblyTitle>Word文档处理工具</AssemblyTitle>
    <AssemblyDescription>基于 VB.NET 的 Word 文档批量处理工具</AssemblyDescription>
    <AssemblyCompany>您的公司</AssemblyCompany>
    <AssemblyProduct>Word文档处理工具</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2025</AssemblyCopyright>

    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>

    <!-- 如果需要托管 COM，可保留 -->
    <EnableComHosting>true</EnableComHosting>
  </PropertyGroup>

  <!-- ================== NuGet 依赖 ================== -->
  <ItemGroup>
    <PackageReference Include="Microsoft.Office.Interop.Word" Version="15.0.4797.1004" />
    <PackageReference Include="System.Drawing.Common" Version="6.0.0" />
  </ItemGroup>

  <!-- ================== 其他文件设置（示例） ================== -->
  <ItemGroup>
    <!-- 若不想编译某文件，可在此移除 -->
    <!-- <Compile Remove="WordProcessorApp.vb" /> -->
  </ItemGroup>

</Project>
