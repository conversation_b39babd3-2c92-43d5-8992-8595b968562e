<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <RootNamespace>WordProcessor</RootNamespace>
    <AssemblyName>WordProcessor</AssemblyName>
    <StartupObject>WordProcessor.Program</StartupObject>
    <AssemblyTitle>Word文档处理工具</AssemblyTitle>
    <AssemblyDescription>基于VB.NET的Word文档批量处理工具</AssemblyDescription>
    <AssemblyCompany>您的公司</AssemblyCompany>
    <AssemblyProduct>Word文档处理工具</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2025</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <EnableComHosting>true</EnableComHosting>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Office.Interop.Word" Version="15.0.4797.1004" />
    <PackageReference Include="System.Drawing.Common" Version="6.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="WordProcessorApp.vb" />
  </ItemGroup>

</Project>