Option Explicit

Sub SongtiTNR(control As IRibbonControl)
    '优化版：调整段落格式，调整编号格式和正文格式，数字使用Times New Roman
    '新增：编号背景颜色设置为无
    
    On Error GoTo ErrorHandler
    
    ' 获取用户设置
    Dim result As String
    result = ShowFormatDialog()
    
    ' 如果用户取消，则退出
    If result = "" Then Exit Sub
    
    ' 解析设置参数
    Dim settings() As String
    settings = Split(result, "|")
    
    Dim numberFontName As String, numberFontSize As String, numberIsBold As String
    Dim textFontName As String, textFontSize As String, textIsBold As String
    
    numberFontName = settings(0)
    numberFontSize = settings(1)
    numberIsBold = settings(2)
    textFontName = settings(3)
    textFontSize = settings(4)
    textIsBold = settings(5)
    
    ' 关闭屏幕更新提高性能
    Application.ScreenUpdating = False
    
    ' 显示进度
    Application.StatusBar = "正在处理格式设置..."
    
    ' 1. 批量处理编号格式（避免重复处理）+ 设置背景颜色为无
    If numberFontName <> "" Then
        Call ProcessNumberingFormat(numberFontName, numberFontSize, numberIsBold)
    End If
    
    ' 2. 批量处理正文格式
    If textFontName <> "" Then
        Call ProcessTextFormat(textFontName, textFontSize, textIsBold)
    End If
    
    ' 3. 一次性处理数字字体（优化的查找替换）
    Call SetNumbersToTimesNewRomanOptimized(Selection.Range)
    
    ' 恢复状态
    Application.StatusBar = False
    Application.ScreenUpdating = True
    
    MsgBox "格式设置完成！编号背景已清除。", vbInformation, "完成"
    Exit Sub
    
ErrorHandler:
    Application.ScreenUpdating = True
    Application.StatusBar = False
    MsgBox "处理过程中发生错误: " & Err.Description, vbExclamation, "错误"
End Sub

' 优化的编号格式处理（新增背景颜色设置）
Sub ProcessNumberingFormat(fontName As String, fontSize As String, isBold As String)
    On Error Resume Next
    
    Dim processedTemplates As Object
    Set processedTemplates = CreateObject("Scripting.Dictionary")
    
    Dim para As Paragraph
    For Each para In Selection.Paragraphs
        If para.Range.ListFormat.ListType <> wdListNoNumbering Then
            Dim lt As ListTemplate
            Dim currentLevel As Long
            Dim templateKey As String
            
            Set lt = para.Range.ListFormat.ListTemplate
            currentLevel = para.Range.ListFormat.ListLevelNumber
            templateKey = ObjPtr(lt) & "_" & currentLevel
            
            ' 使用Dictionary避免重复处理
            If Not processedTemplates.Exists(templateKey) And Not lt Is Nothing Then
                With lt.ListLevels(currentLevel).Font
                    .Name = fontName
                    .NameFarEast = fontName
                    If fontSize <> "" And IsNumeric(fontSize) Then
                        .Size = CDbl(fontSize)
                    End If
                    If UCase(isBold) = "TRUE" Then
                        .Bold = True
                    ElseIf UCase(isBold) = "FALSE" Then
                        .Bold = False
                    End If
                    ' 新增：设置编号背景颜色为无
                    .Shading.BackgroundPatternColor = wdColorAutomatic
                End With
                processedTemplates.Add templateKey, True
            End If
        End If
    Next para
    
    Set processedTemplates = Nothing
    On Error GoTo 0
End Sub

' 优化的正文格式处理
Sub ProcessTextFormat(fontName As String, fontSize As String, isBold As String)
    On Error Resume Next
    
    ' 直接对整个选择区域进行格式设置，而不是逐段处理
    With Selection.Font
        .Name = fontName
        .NameFarEast = fontName
        If fontSize <> "" And IsNumeric(fontSize) Then
            .Size = CDbl(fontSize)
        End If
        If UCase(isBold) = "TRUE" Then
            .Bold = True
        ElseIf UCase(isBold) = "FALSE" Then
            .Bold = False
        End If
    End With
    
    On Error GoTo 0
End Sub

' 优化的数字字体设置
Sub SetNumbersToTimesNewRomanOptimized(targetRange As Range)
    On Error Resume Next
    
    Dim findRange As Range
    Set findRange = targetRange.Duplicate
    
    ' 一次性处理所有数字字符（包括小数点、逗号、百分号等）
    With findRange.Find
        .ClearFormatting
        .Replacement.ClearFormatting
        .Text = "[0-9.,%-]@"  ' 匹配数字及相关符号的组合
        .Replacement.Text = "^&"
        .Replacement.Font.Name = "Times New Roman"
        .Replacement.Font.NameAscii = "Times New Roman"
        .Forward = True
        .Wrap = wdFindStop
        .Format = True
        .MatchWildcards = True
        .Execute Replace:=wdReplaceAll
    End With
    
    On Error GoTo 0
End Sub

' 简化的对话框函数
Function ShowFormatDialog() As String
    Dim msg As String
    Dim title As String
    Dim defaultValues As String
    Dim userInput As String
    
    msg = "请输入格式设置参数（用逗号分隔）：" & vbCrLf & vbCrLf & _
          "格式：编号字体,编号字号,编号粗体,正文字体,正文字号,正文粗体" & vbCrLf & _
          "示例：宋体,10.5,否,宋体,10.5,否" & vbCrLf & _
          "说明：留空表示不修改该项，粗体用'是'或'否'" & vbCrLf & _
          "注意：数字将自动设为Times New Roman，编号背景将清除"
    
    title = "字体格式设置"
    defaultValues = "宋体,10.5,否,宋体,10.5,否"
    
    userInput = InputBox(msg, title, defaultValues)
    
    ' 用户取消
    If userInput = "" Then
        ShowFormatDialog = ""
        Exit Function
    End If
    
    ' 解析输入
    Dim inputs() As String
    inputs = Split(userInput, ",")
    
    ' 检查参数数量
    If UBound(inputs) < 5 Then
        MsgBox "参数不足！请输入6个参数。", vbExclamation, "输入错误"
        ShowFormatDialog = ""
        Exit Function
    End If
    
    ' 处理粗体设置
    Dim numberBold As String, textBold As String
    numberBold = IIf(InStr(UCase(Trim(inputs(2))), "是") > 0, "TRUE", "FALSE")
    textBold = IIf(InStr(UCase(Trim(inputs(5))), "是") > 0, "TRUE", "FALSE")
    
    ' 返回格式化参数
    ShowFormatDialog = Trim(inputs(0)) & "|" & Trim(inputs(1)) & "|" & numberBold & "|" & _
                      Trim(inputs(3)) & "|" & Trim(inputs(4)) & "|" & textBold
End Function

' 快速版本 - 仅处理基本格式（包含背景清除）
Sub SongtiTNR_Fast(control As IRibbonControl)
    '快速版本：使用预设参数，跳过对话框
    
    Application.ScreenUpdating = False
    
    ' 预设参数：宋体，10.5磅，不加粗
    With Selection.Font
        .Name = "宋体"
        .NameFarEast = "宋体"
        .Size = 10.5
        .Bold = False
    End With
    
    ' 处理编号背景颜色
    Call ClearNumberingBackground()
    
    ' 设置数字为Times New Roman
    Call SetNumbersToTimesNewRomanOptimized(Selection.Range)
    
    Application.ScreenUpdating = True
    
    MsgBox "快速格式设置完成！编号背景已清除。", vbInformation, "完成"
End Sub

' 清除编号背景颜色的独立函数
Sub ClearNumberingBackground()
    On Error Resume Next
    
    Dim processedTemplates As Object
    Set processedTemplates = CreateObject("Scripting.Dictionary")
    
    Dim para As Paragraph
    For Each para In Selection.Paragraphs
        If para.Range.ListFormat.ListType <> wdListNoNumbering Then
            Dim lt As ListTemplate
            Dim currentLevel As Long
            Dim templateKey As String
            
            Set lt = para.Range.ListFormat.ListTemplate
            currentLevel = para.Range.ListFormat.ListLevelNumber
            templateKey = ObjPtr(lt) & "_" & currentLevel
            
            ' 避免重复处理
            If Not processedTemplates.Exists(templateKey) And Not lt Is Nothing Then
                ' 仅设置背景颜色为无
                lt.ListLevels(currentLevel).Font.Shading.BackgroundPatternColor = wdColorAutomatic
                processedTemplates.Add templateKey, True
            End If
        End If
    Next para
    
    Set processedTemplates = Nothing
    On Error GoTo 0
End Sub

' 仅处理数字字体
Sub NumbersToTNR(control As IRibbonControl)
    '仅将数字设置为Times New Roman
    
    Application.ScreenUpdating = False
    Call SetNumbersToTimesNewRomanOptimized(Selection.Range)
    Application.ScreenUpdating = True
    
    MsgBox "数字字体已设置为Times New Roman！", vbInformation, "完成"
End Sub

' 仅清除编号背景
Sub ClearNumberBackground(control As IRibbonControl)
    '仅清除编号背景颜色
    
    Application.ScreenUpdating = False
    Call ClearNumberingBackground()
    Application.ScreenUpdating = True
    
    MsgBox "编号背景颜色已清除！", vbInformation, "完成"
End Sub