
import sys
import os
from docx import Document
import re

def format_numbers_in_doc(doc_path, output_path):
    doc = Document(doc_path)
    for para in doc.paragraphs:
        original_text = para.text
        new_text = re.sub(r'(\d+\.\d+|\d+)', lambda x: "{:,.2f}".format(float(x.group())), original_text)
        para.text = new_text
    doc.save(output_path)

def sum_numbers_in_table(doc_path, table_index):
    doc = Document(doc_path)
    table = doc.tables[table_index]
    total_sum = 0
    for row in table.rows:
        for cell in row.cells:
            numbers = re.findall(r'[\d.]+', cell.text)
            total_sum += sum([float(num) for num in numbers if num])
    return total_sum

def check_table_sum(doc_path, table_index, check_type='row'):
    doc = Document(doc_path)
    table = doc.tables[table_index]
    check_results = []
    if check_type == 'row':
        for i, row in enumerate(table.rows):
            row_sum = sum(float(cell.text) for cell in row.cells if re.match(r'^\d+(\.\d+)?$', cell.text))
            check_results.append((f'Row {i+1}', row_sum))
    elif check_type == 'column':
        for i in range(len(table.columns)):
            col_sum = sum(float(row.cells[i].text) for row in table.rows if re.match(r'^\d+(\.\d+)?$', row.cells[i].text))
            check_results.append((f'Column {i+1}', col_sum))
    return check_results

def main():
    print("欢迎使用Word文档自动处理工具")
    print("请选择功能:")
    print("1. 格式化文档中所有数字")
    print("2. 计算Word表格内数字总和")
    print("3. 检查Word表格内数字总和（行/列）")
    choice = input("请输入你的选择（1-3）：")

    if choice == '1':
        input_path = input("请输入Word文档路径：")
        output_path = input("请输入保存格式化后的文档路径：")
        format_numbers_in_doc(input_path, output_path)
        print(f"文档已格式化完成并保存至 {output_path}")

    elif choice == '2':
        input_path = input("请输入Word文档路径：")
        table_index = int(input("请输入表格序号（从0开始）："))
        total = sum_numbers_in_table(input_path, table_index)
        print(f"表格内所有数字的总和为：{total:,.2f}")

    elif choice == '3':
        input_path = input("请输入Word文档路径：")
        table_index = int(input("请输入表格序号（从0开始）："))
        check_type = input("请输入检查类型（row/column）：").strip().lower()
        results = check_table_sum(input_path, table_index, check_type)
        print(f"{check_type} 检查结果如下：")
        for name, value in results:
            print(f"{name}: {value:,.2f}")

    else:
        print("无效选择，请重新运行程序选择1-3功能。")

if __name__ == "__main__":
    main()
