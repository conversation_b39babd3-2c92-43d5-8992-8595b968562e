#include <iostream>
#include <string>
#include <vector>
#include <map>
#include <sstream>
#include <windows.h>
#include <comdef.h>
#include <comutil.h>

// 导入Word类型库 - 请根据您的Office版本调整路径
#import "C:\\Program Files\\Microsoft Office\\root\\Office16\\MSWORD.OLB" \
    rename("ExitWindows", "WordExitWindows") \
    rename("FindText", "WordFindText") \
    rename("RGB", "WordRGB")

using namespace Word;

// 前向声明
void ProcessNumberingFormat(const std::string& fontName, double fontSize, bool isBold, SelectionPtr selection);
void ProcessTextFormat(const std::string& fontName, double fontSize, bool isBold, SelectionPtr selection);
void SetNumbersToTimesNewRomanOptimized(RangePtr targetRange);
std::string ShowFormatDialog();
std::vector<std::string> Split(const std::string& str, char delimiter);
std::string Trim(const std::string& str);

void SongtiTNR(SelectionPtr selection)
{
    try
    {
        // 显示对话框，用户输入
        std::string input = ShowFormatDialog();
        if (input.empty()) return;

        // 解析设置参数
        std::vector<std::string> settings = Split(input, '|');
        if (settings.size() < 6) {
            MessageBoxA(NULL, "参数不足！", "错误", MB_ICONEXCLAMATION);
            return;
        }

        std::string numberFontName = Trim(settings[0]);
        std::string numberFontSize = Trim(settings[1]);
        bool numberIsBold = (settings[2] == "TRUE" || settings[2] == "是");
        std::string textFontName = Trim(settings[3]);
        std::string textFontSize = Trim(settings[4]);
        bool textIsBold = (settings[5] == "TRUE" || settings[5] == "是");

        // 关闭屏幕更新
        selection->Application->PutScreenUpdating(VARIANT_FALSE);
        selection->Application->PutStatusBar(_variant_t(L"正在处理格式设置..."));

        // 1. 先设置正文格式
        if (!textFontName.empty())
            ProcessTextFormat(textFontName, atof(textFontSize.c_str()), textIsBold, selection);

        // 2. 再设置编号格式
        if (!numberFontName.empty())
            ProcessNumberingFormat(numberFontName, atof(numberFontSize.c_str()), numberIsBold, selection);

        // 3. 数字字体一次性优化处理
        SetNumbersToTimesNewRomanOptimized(selection->GetRange());

        // 恢复状态
        selection->Application->PutStatusBar(_variant_t(false));
        selection->Application->PutScreenUpdating(VARIANT_TRUE);

        MessageBoxA(NULL, "格式设置完成！", "完成", MB_ICONINFORMATION);
    }
    catch (const _com_error& e)
    {
        std::string errorMsg = "处理过程中发生错误: ";
        _bstr_t bstrError(e.Description());
        errorMsg += (char*)bstrError;
        MessageBoxA(NULL, errorMsg.c_str(), "错误", MB_ICONEXCLAMATION);
        
        // 确保恢复状态
        try {
            selection->Application->PutScreenUpdating(VARIANT_TRUE);
            selection->Application->PutStatusBar(_variant_t(false));
        } catch (...) {}
    }
}

// 编号格式处理函数
void ProcessNumberingFormat(const std::string& fontName, double fontSize, bool isBold, SelectionPtr selection)
{
    try
    {
        std::map<std::string, bool> processedTemplates;

        ParagraphsPtr paragraphs = selection->GetParagraphs();
        long paraCount = paragraphs->GetCount();

        for (long i = 1; i <= paraCount; i++)
        {
            ParagraphPtr para = paragraphs->Item(i);
            ListFormatPtr listFormat = para->GetRange()->GetListFormat();
            
            if (listFormat->GetListType() != wdListNoNumbering)
            {
                ListTemplatePtr lt = listFormat->GetListTemplate();
                long level = listFormat->GetListLevelNumber();
                
                // 创建唯一键
                char templateKey[64];
                sprintf_s(templateKey, sizeof(templateKey), "%p_%ld", 
                         static_cast<void*>(lt.GetInterfacePtr()), level);

                if (processedTemplates.find(templateKey) == processedTemplates.end() && lt)
                {
                    ListLevelsPtr listLevels = lt->GetListLevels();
                    ListLevelPtr listLevel = listLevels->Item(level);
                    FontPtr font = listLevel->GetFont();

                    font->PutName(_bstr_t(fontName.c_str()));
                    font->PutNameFarEast(_bstr_t(fontName.c_str()));
                    font->PutNameAscii(_bstr_t(fontName.c_str()));
                    
                    if (fontSize > 0) 
                        font->PutSize(fontSize);
                    
                    font->PutBold(isBold ? VARIANT_TRUE : VARIANT_FALSE);
                    
                    // 设置背景颜色为无
                    ShadingPtr shading = font->GetShading();
                    shading->PutBackgroundPatternColor(wdColorAutomatic);

                    processedTemplates[templateKey] = true;
                }
            }
        }
    }
    catch (const _com_error& e)
    {
        std::string errorMsg = "处理编号格式时发生错误: ";
        _bstr_t bstrError(e.Description());
        errorMsg += (char*)bstrError;
        MessageBoxA(NULL, errorMsg.c_str(), "错误", MB_ICONEXCLAMATION);
    }
}

// 正文格式处理函数
void ProcessTextFormat(const std::string& fontName, double fontSize, bool isBold, SelectionPtr selection)
{
    try
    {
        FontPtr font = selection->GetFont();
        font->PutName(_bstr_t(fontName.c_str()));
        font->PutNameFarEast(_bstr_t(fontName.c_str()));
        font->PutNameAscii(_bstr_t(fontName.c_str()));
        
        if (fontSize > 0) 
            font->PutSize(fontSize);
        
        font->PutBold(isBold ? VARIANT_TRUE : VARIANT_FALSE);
    }
    catch (const _com_error& e)
    {
        std::string errorMsg = "处理正文格式时发生错误: ";
        _bstr_t bstrError(e.Description());
        errorMsg += (char*)bstrError;
        MessageBoxA(NULL, errorMsg.c_str(), "错误", MB_ICONEXCLAMATION);
    }
}

// 数字字体优化处理函数
void SetNumbersToTimesNewRomanOptimized(RangePtr targetRange)
{
    try
    {
        RangePtr findRange = targetRange->Duplicate();
        FindPtr find = findRange->GetFind();
        
        find->ClearFormatting();
        find->GetReplacement()->ClearFormatting();
        
        find->PutText(_bstr_t(L"[0-9.,%-]@"));
        find->GetReplacement()->PutText(_bstr_t(L"^&"));
        
        FontPtr replaceFont = find->GetReplacement()->GetFont();
        replaceFont->PutName(_bstr_t(L"Times New Roman"));
        replaceFont->PutNameAscii(_bstr_t(L"Times New Roman"));
        
        find->PutForward(VARIANT_TRUE);
        find->PutWrap(wdFindStop);
        find->PutFormat(VARIANT_TRUE);
        find->PutMatchWildcards(VARIANT_TRUE);
        
        find->Execute(
            _variant_t(),           // FindText
            _variant_t(false),      // MatchCase
            _variant_t(false),      // MatchWholeWord
            _variant_t(true),       // MatchWildcards
            _variant_t(false),      // MatchSoundsLike
            _variant_t(false),      // MatchAllWordForms
            _variant_t(true),       // Forward
            _variant_t(wdFindStop), // Wrap
            _variant_t(true),       // Format
            _variant_t(),           // ReplaceWith
            _variant_t(wdReplaceAll) // Replace
        );
    }
    catch (const _com_error& e)
    {
        std::string errorMsg = "处理数字字体时发生错误: ";
        _bstr_t bstrError(e.Description());
        errorMsg += (char*)bstrError;
        MessageBoxA(NULL, errorMsg.c_str(), "错误", MB_ICONEXCLAMATION);
    }
}

// 显示格式对话框
std::string ShowFormatDialog()
{
    std::string defaultValues = "宋体,10.5,否,宋体,10.5,否";
    std::string userInput;
    
    std::cout << "请输入格式设置参数（用逗号分隔）：" << std::endl;
    std::cout << "格式：编号字体,编号字号,编号粗体,正文字体,正文字号,正文粗体" << std::endl;
    std::cout << "示例：" << defaultValues << std::endl;
    std::cout << "说明：留空表示不修改该项，粗体用'是'或'否'" << std::endl;
    std::cout << "注意：数字将自动设为Times New Roman" << std::endl;
    std::cout << "请输入: ";
    
    std::getline(std::cin, userInput);
    
    if (userInput.empty()) {
        userInput = defaultValues;
    }
    
    // 转换为内部格式
    std::vector<std::string> inputs = Split(userInput, ',');
    if (inputs.size() < 6) {
        return "";
    }
    
    // 处理粗体设置
    std::string numberBold = (inputs[2].find("是") != std::string::npos) ? "TRUE" : "FALSE";
    std::string textBold = (inputs[5].find("是") != std::string::npos) ? "TRUE" : "FALSE";
    
    // 返回格式化参数
    return inputs[0] + "|" + inputs[1] + "|" + numberBold + "|" + 
           inputs[3] + "|" + inputs[4] + "|" + textBold;
}

// 辅助函数：字符串拆分
std::vector<std::string> Split(const std::string& str, char delimiter)
{
    std::vector<std::string> tokens;
    std::stringstream ss(str);
    std::string token;
    
    while (std::getline(ss, token, delimiter)) {
        tokens.push_back(Trim(token));
    }
    
    return tokens;
}

// 辅助函数：去除前后空格
std::string Trim(const std::string& str)
{
    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) return "";
    
    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

// 主函数
int main()
{
    // 设置控制台编码
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);
    
    // 初始化COM
    CoInitialize(nullptr);
    
    try
    {
        // 连接到Word应用程序
        _ApplicationPtr wordApp;
        HRESULT hr = wordApp.GetActiveObject(L"Word.Application");
        
        if (FAILED(hr)) {
            hr = wordApp.CreateInstance(L"Word.Application");
            if (FAILED(hr)) {
                MessageBoxA(NULL, "无法连接到Word应用程序", "错误", MB_ICONERROR);
                CoUninitialize();
                return -1;
            }
        }
        
        // 获取当前选择
        SelectionPtr selection = wordApp->GetSelection();
        
        if (selection->GetType() == wdSelectionIP) {
            MessageBoxA(NULL, "请先选择需要格式化的文本！", "提示", MB_ICONINFORMATION);
        } else {
            // 调用主要功能
            SongtiTNR(selection);
        }
    }
    catch (const _com_error& e)
    {
        std::string errorMsg = "程序运行时发生错误: ";
        _bstr_t bstrError(e.Description());
        errorMsg += (char*)bstrError;
        MessageBoxA(NULL, errorMsg.c_str(), "错误", MB_ICONERROR);
    }
    
    CoUninitialize();
    
    std::cout << "按任意键退出...";
    std::cin.get();
    
    return 0;
}

// 快速版本函数
void SongtiTNR_Fast(SelectionPtr selection)
{
    try
    {
        selection->Application->PutScreenUpdating(VARIANT_FALSE);
        
        // 设置正文格式
        ProcessTextFormat("宋体", 10.5, false, selection);
        
        // 设置编号格式
        ProcessNumberingFormat("宋体", 10.5, false, selection);
        
        // 设置数字字体
        SetNumbersToTimesNewRomanOptimized(selection->GetRange());
        
        selection->Application->PutScreenUpdating(VARIANT_TRUE);
        
        MessageBoxA(NULL, "快速格式设置完成！", "完成", MB_ICONINFORMATION);
    }
    catch (const _com_error& e)
    {
        selection->Application->PutScreenUpdating(VARIANT_TRUE);
        std::string errorMsg = "快速设置时发生错误: ";
        _bstr_t bstrError(e.Description());
        errorMsg += (char*)bstrError;
        MessageBoxA(NULL, errorMsg.c_str(), "错误", MB_ICONEXCLAMATION);
    }
}