Option Explicit

' AI大模型调用 - 带配置界面版本
' 支持弹出窗口配置API密钥

' 全局变量存储API配置
Public openaiKey As String
Public claudeKey As String
Public qwenKey As String

' 主调用函数
Sub AIDY(control As IRibbonControl)
    On Error GoTo AIErrorHandler
    
    ' 声明所有变量
    Dim prompt As String
    Dim aiResults As String
    Dim http As Object
    Dim jsonBody As String
    Dim response As String
    Dim i As Integer
    Dim startPos As Long
    Dim endPos As Long
    Dim content As String
    
    ' API配置数组
    Dim apiNames(1 To 3) As String
    Dim apiURLs(1 To 3) As String
    Dim apiKeys(1 To 3) As String
    Dim apiModels(1 To 3) As String
    Dim apiEnabled(1 To 3) As Boolean
    
    ' 检查是否已配置API，如果没有则弹出配置窗口
    On Error Resume Next
    LoadAPISettings
    On Error GoTo AIErrorHandler
    
    If openaiKey = "" And claudeKey = "" And qwenKey = "" Then
        If MsgBox("检测到未配置API密钥，是否现在配置？", vbYesNo + vbQuestion, "API配置") = vbYes Then
            On Error Resume Next
            ShowAPIConfigDialog
            LoadAPISettings
            On Error GoTo AIErrorHandler
        Else
            Exit Sub
        End If
    End If
    
    ' 配置API信息
    apiNames(1) = "OpenAI"
    apiURLs(1) = "https://api.openai.com/v1/chat/completions"
    apiKeys(1) = openaiKey
    apiModels(1) = "gpt-3.5-turbo"
    apiEnabled(1) = (openaiKey <> "")
    
    apiNames(2) = "Claude"
    apiURLs(2) = "https://api.anthropic.com/v1/messages"
    apiKeys(2) = claudeKey
    apiModels(2) = "claude-3-sonnet-20240229"
    apiEnabled(2) = (claudeKey <> "")
    
    apiNames(3) = "通义千问"
    apiURLs(3) = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
    apiKeys(3) = qwenKey
    apiModels(3) = "qwen-turbo"
    apiEnabled(3) = (qwenKey <> "")
    
    ' 获取用户输入的问题
    prompt = InputBox("请输入要发送给AI的问题：", "AI助手", "你好，请介绍一下自己")
    If prompt = "" Then GoTo AICallEnd
    
    aiResults = "AI调用结果：" & vbCrLf & String(50, "=") & vbCrLf & vbCrLf
    
    ' 循环调用所有启用的API
    For i = 1 To 3
        If apiEnabled(i) Then
            ' 创建HTTP对象
            Set http = CreateObject("MSXML2.XMLHTTP")
            
            ' 根据不同API构建JSON请求体
            Select Case apiNames(i)
                Case "OpenAI"
                    jsonBody = "{" & _
                        """model"":""" & apiModels(i) & """," & _
                        """messages"":[{" & _
                            """role"":""user""," & _
                            """content"":""" & EscapeJSON(prompt) & """" & _
                        "}]," & _
                        """max_tokens"":1000," & _
                        """temperature"":0.7" & _
                    "}"
                    
                Case "Claude"
                    jsonBody = "{" & _
                        """model"":""" & apiModels(i) & """," & _
                        """max_tokens"":1000," & _
                        """messages"":[{" & _
                            """role"":""user""," & _
                            """content"":""" & EscapeJSON(prompt) & """" & _
                        "}]" & _
                    "}"
                    
                Case "通义千问"
                    jsonBody = "{" & _
                        """model"":""" & apiModels(i) & """," & _
                        """input"":{" & _
                            """messages"":[{" & _
                                """role"":""user""," & _
                                """content"":""" & EscapeJSON(prompt) & """" & _
                            "}]" & _
                        "}," & _
                        """parameters"":{" & _
                            """max_tokens"":1000," & _
                            """temperature"":0.7" & _
                        "}" & _
                    "}"
            End Select
            
            ' 发送HTTP请求
            With http
                .Open "POST", apiURLs(i), False
                .setRequestHeader "Content-Type", "application/json"
                .setRequestHeader "Authorization", "Bearer " & apiKeys(i)
                
                ' 设置特殊请求头
                If apiNames(i) = "Claude" Then
                    .setRequestHeader "anthropic-version", "2023-06-01"
                ElseIf apiNames(i) = "通义千问" Then
                    .setRequestHeader "X-DashScope-SSE", "disable"
                End If
                
                .send jsonBody
                
                ' 处理响应
                If .Status = 200 Then
                    response = .responseText
                    content = ParseAPIResponse(apiNames(i), response)
                    
                    If content <> "" Then
                        aiResults = aiResults & "【" & apiNames(i) & "】" & vbCrLf & content & vbCrLf & vbCrLf
                    Else
                        aiResults = aiResults & "【" & apiNames(i) & "】解析响应失败" & vbCrLf & vbCrLf
                    End If
                Else
                    aiResults = aiResults & "【" & apiNames(i) & "】调用失败 (状态码: " & .Status & ")" & vbCrLf & vbCrLf
                End If
            End With
            
            Set http = Nothing
        End If
    Next i
    
    ' 显示结果
    If Len(aiResults) > 100 Then
        ShowResultDialog aiResults
    Else
        MsgBox "没有获取到AI响应，请检查API配置", vbExclamation, "提示"
    End If
    
AICallEnd:
    Exit Sub
    
AIErrorHandler:
    MsgBox "AI调用发生错误: " & Err.Description, vbExclamation, "错误"
    If Not http Is Nothing Then Set http = Nothing
    Resume AICallEnd
End Sub

' API配置对话框
Sub ShowAPIConfigDialog()
    Dim configForm As String
    Dim userInput As String
    Dim tempOpenAI As String, tempClaude As String, tempQwen As String
    
    ' 加载当前设置
    LoadAPISettings
    
    ' 创建配置界面
    configForm = "AI API 配置" & vbCrLf & String(40, "=") & vbCrLf & vbCrLf & _
                "请输入您的API密钥（可选择性配置）：" & vbCrLf & vbCrLf & _
                "1. OpenAI API Key" & vbCrLf & _
                "2. Claude API Key" & vbCrLf & _
                "3. 通义千问 API Key" & vbCrLf & vbCrLf & _
                "请选择要配置的API (输入1、2或3)："
    
    userInput = InputBox(configForm, "API配置选择", "1")
    
    Select Case userInput
        Case "1"
            tempOpenAI = InputBox("请输入OpenAI API Key：" & vbCrLf & vbCrLf & _
                                "获取地址：https://platform.openai.com/api-keys" & vbCrLf & _
                                "格式：sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", _
                                "OpenAI配置", openaiKey)
            If tempOpenAI <> "" Then
                openaiKey = tempOpenAI
                SaveAPISettings
                MsgBox "OpenAI API Key 配置成功！", vbInformation, "配置完成"
            End If
            
        Case "2"
            tempClaude = InputBox("请输入Claude API Key：" & vbCrLf & vbCrLf & _
                                "获取地址：https://console.anthropic.com/" & vbCrLf & _
                                "格式：sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", _
                                "Claude配置", claudeKey)
            If tempClaude <> "" Then
                claudeKey = tempClaude
                SaveAPISettings
                MsgBox "Claude API Key 配置成功！", vbInformation, "配置完成"
            End If
            
        Case "3"
            tempQwen = InputBox("请输入通义千问 API Key：" & vbCrLf & vbCrLf & _
                              "获取地址：https://dashscope.console.aliyun.com/" & vbCrLf & _
                              "格式：sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", _
                              "通义千问配置", qwenKey)
            If tempQwen <> "" Then
                qwenKey = tempQwen
                SaveAPISettings
                MsgBox "通义千问 API Key 配置成功！", vbInformation, "配置完成"
            End If
    End Select
    
    ' 询问是否继续配置其他API
    If MsgBox("是否继续配置其他API？", vbYesNo + vbQuestion, "继续配置") = vbYes Then
        ShowAPIConfigDialog
    End If
End Sub

' 显示结果对话框
Sub ShowResultDialog(results As String)
    Dim choice As VbMsgBoxResult
    
    ' 如果结果太长，截取前1000个字符显示
    Dim displayResults As String
    If Len(results) > 1000 Then
        displayResults = Left(results, 1000) & vbCrLf & vbCrLf & "... (结果已截取，完整内容请查看文档)"
    Else
        displayResults = results
    End If
    
    choice = MsgBox(displayResults & vbCrLf & vbCrLf & "是否将结果插入到当前文档？", _
                   vbYesNoCancel + vbInformation, "AI调用结果")
    
    Select Case choice
        Case vbYes
            ' 插入到当前文档
            Selection.TypeText results
        Case vbNo
            ' 仅显示，不插入
        Case vbCancel
            ' 取消
    End Select
End Sub

' 加载API设置（从文档变量）
Sub LoadAPISettings()
    On Error Resume Next
    
    ' 初始化为空字符串
    openaiKey = ""
    claudeKey = ""
    qwenKey = ""
    
    ' 尝试从文档变量中读取API密钥
    Dim doc As Document
    Set doc = ActiveDocument
    
    If Not doc Is Nothing Then
        ' 检查变量是否存在并读取
        Dim i As Integer
        For i = 1 To doc.Variables.Count
            Select Case doc.Variables(i).Name
                Case "OpenAI_Key"
                    openaiKey = doc.Variables(i).Value
                Case "Claude_Key"
                    claudeKey = doc.Variables(i).Value
                Case "Qwen_Key"
                    qwenKey = doc.Variables(i).Value
            End Select
        Next i
    End If
    
    On Error GoTo 0
End Sub

' 保存API设置到文档变量
Sub SaveAPISettings()
    On Error Resume Next
    
    Dim doc As Document
    Set doc = ActiveDocument
    
    If Not doc Is Nothing Then
        ' 保存OpenAI Key
        UpdateOrAddVariable doc, "OpenAI_Key", openaiKey
        ' 保存Claude Key
        UpdateOrAddVariable doc, "Claude_Key", claudeKey
        ' 保存Qwen Key
        UpdateOrAddVariable doc, "Qwen_Key", qwenKey
    End If
    
    On Error GoTo 0
End Sub

' 更新或添加文档变量的辅助函数
Sub UpdateOrAddVariable(doc As Document, varName As String, varValue As String)
    On Error Resume Next
    
    Dim found As Boolean
    Dim i As Integer
    found = False
    
    ' 查找是否已存在该变量
    For i = 1 To doc.Variables.Count
        If doc.Variables(i).Name = varName Then
            doc.Variables(i).Value = varValue
            found = True
            Exit For
        End If
    Next i
    
    ' 如果不存在则添加新变量
    If Not found Then
        doc.Variables.Add varName, varValue
    End If
    
    On Error GoTo 0
End Sub

' JSON字符串转义函数
Function EscapeJSON(text As String) As String
    Dim result As String
    result = text
    result = Replace(result, "\", "\\")
    result = Replace(result, """", "\""")
    result = Replace(result, vbCrLf, "\n")
    result = Replace(result, vbCr, "\n")
    result = Replace(result, vbLf, "\n")
    result = Replace(result, vbTab, "\t")
    EscapeJSON = result
End Function

' 解析API响应
Function ParseAPIResponse(apiName As String, responseText As String) As String
    On Error GoTo ParseError
    
    Dim content As String
    Dim startPos As Long, endPos As Long
    
    Select Case apiName
        Case "OpenAI", "通义千问"
            startPos = InStr(responseText, """content"":""")
            If startPos > 0 Then
                startPos = startPos + 11
                endPos = InStr(startPos, responseText, """")
                If endPos > startPos Then
                    content = Mid(responseText, startPos, endPos - startPos)
                End If
            End If
            
        Case "Claude"
            startPos = InStr(responseText, """text"":""")
            If startPos > 0 Then
                startPos = startPos + 8
                endPos = InStr(startPos, responseText, """")
                If endPos > startPos Then
                    content = Mid(responseText, startPos, endPos - startPos)
                End If
            End If
    End Select
    
    ' 反转义JSON字符串
    If content <> "" Then
        content = Replace(Replace(Replace(content, "\n", vbCrLf), "\""", """"), "\\", "\")
        ParseAPIResponse = content
    Else
        ParseAPIResponse = ""
    End If
    
    Exit Function
    
ParseError:
    ParseAPIResponse = "解析响应时发生错误：" & Err.Description
End Function

' API配置管理界面
Sub ConfigureAIAPIs()
    Dim choice As String
    Dim configMenu As String
    
    LoadAPISettings
    
    Dim openaiStatus As String, claudeStatus As String, qwenStatus As String
    
    If openaiKey <> "" Then openaiStatus = "已配置" Else openaiStatus = "未配置"
    If claudeKey <> "" Then claudeStatus = "已配置" Else claudeStatus = "未配置"
    If qwenKey <> "" Then qwenStatus = "已配置" Else qwenStatus = "未配置"
    
    configMenu = "AI API 配置管理" & vbCrLf & String(30, "=") & vbCrLf & vbCrLf & _
                "当前配置状态：" & vbCrLf & _
                "1. OpenAI: " & openaiStatus & vbCrLf & _
                "2. Claude: " & claudeStatus & vbCrLf & _
                "3. 通义千问: " & qwenStatus & vbCrLf & vbCrLf & _
                "请选择操作：" & vbCrLf & _
                "1-3: 配置对应API" & vbCrLf & _
                "4: 清除所有配置" & vbCrLf & _
                "5: 测试API连接"
    
    choice = InputBox(configMenu, "API配置管理", "1")
    
    Select Case choice
        Case "1"
            ConfigureSingleAPI "OpenAI", "https://platform.openai.com/api-keys", "sk-"
        Case "2"
            ConfigureSingleAPI "Claude", "https://console.anthropic.com/", "sk-ant-"
        Case "3"
            ConfigureSingleAPI "通义千问", "https://dashscope.console.aliyun.com/", "sk-"
        Case "4"
            ClearAllAPIKeys
        Case "5"
            TestAPIConnections
        Case Else
            Exit Sub
    End Select
End Sub

' 配置单个API
Sub ConfigureSingleAPI(apiName As String, helpURL As String, keyPrefix As String)
    Dim newKey As String
    Dim prompt As String
    Dim currentKey As String
    
    ' 获取当前密钥值
    Select Case apiName
        Case "OpenAI"
            currentKey = openaiKey
        Case "Claude"
            currentKey = claudeKey
        Case "通义千问"
            currentKey = qwenKey
    End Select
    
    prompt = "配置 " & apiName & " API Key" & vbCrLf & vbCrLf & _
            "获取地址：" & helpURL & vbCrLf & _
            "密钥格式：" & keyPrefix & "xxxxxxxxxxxxxxxx" & vbCrLf & vbCrLf & _
            "请输入您的API Key："
    
    newKey = InputBox(prompt, apiName & " 配置", currentKey)
    
    If newKey <> "" Then
        Select Case apiName
            Case "OpenAI"
                openaiKey = newKey
            Case "Claude"
                claudeKey = newKey
            Case "通义千问"
                qwenKey = newKey
        End Select
        
        SaveAPISettings
        MsgBox apiName & " API Key 配置成功！", vbInformation, "配置完成"
    End If
End Sub

' 清除所有API密钥
Sub ClearAllAPIKeys()
    If MsgBox("确定要清除所有API配置吗？", vbYesNo + vbQuestion, "确认清除") = vbYes Then
        openaiKey = ""
        claudeKey = ""
        qwenKey = ""
        SaveAPISettings
        MsgBox "所有API配置已清除", vbInformation, "清除完成"
    End If
End Sub

' 测试API连接
Sub TestAPIConnections()
    Dim testResults As String
    Dim testPrompt As String
    
    LoadAPISettings
    testPrompt = "Hello"
    testResults = "API连接测试结果：" & vbCrLf & String(30, "=") & vbCrLf & vbCrLf
    
    ' 测试OpenAI
    If openaiKey <> "" Then
        testResults = testResults & "OpenAI: " & TestSingleAPI("OpenAI", openaiKey, testPrompt) & vbCrLf
    Else
        testResults = testResults & "OpenAI: 未配置" & vbCrLf
    End If
    
    ' 测试Claude
    If claudeKey <> "" Then
        testResults = testResults & "Claude: " & TestSingleAPI("Claude", claudeKey, testPrompt) & vbCrLf
    Else
        testResults = testResults & "Claude: 未配置" & vbCrLf
    End If
    
    ' 测试通义千问
    If qwenKey <> "" Then
        testResults = testResults & "通义千问: " & TestSingleAPI("通义千问", qwenKey, testPrompt) & vbCrLf
    Else
        testResults = testResults & "通义千问: 未配置" & vbCrLf
    End If
    
    MsgBox testResults, vbInformation, "连接测试结果"
End Sub

' 测试单个API连接
Function TestSingleAPI(apiName As String, apiKey As String, testPrompt As String) As String
    On Error GoTo TestError
    
    Dim http As Object
    Dim jsonBody As String
    Dim apiURL As String
    
    Set http = CreateObject("MSXML2.XMLHTTP")
    
    Select Case apiName
        Case "OpenAI"
            apiURL = "https://api.openai.com/v1/chat/completions"
            jsonBody = "{""model"":""gpt-3.5-turbo"",""messages"":[{""role"":""user"",""content"":""" & testPrompt & """}],""max_tokens"":10}"
        Case "Claude"
            apiURL = "https://api.anthropic.com/v1/messages"
            jsonBody = "{""model"":""claude-3-sonnet-20240229"",""max_tokens"":10,""messages"":[{""role"":""user"",""content"":""" & testPrompt & """}]}"
        Case "通义千问"
            apiURL = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
            jsonBody = "{""model"":""qwen-turbo"",""input"":{""messages"":[{""role"":""user"",""content"":""" & testPrompt & """}]},""parameters"":{""max_tokens"":10}}"
    End Select
    
    With http
        .Open "POST", apiURL, False
        .setRequestHeader "Content-Type", "application/json"
        .setRequestHeader "Authorization", "Bearer " & apiKey
        
        If apiName = "Claude" Then
            .setRequestHeader "anthropic-version", "2023-06-01"
        End If
        
        .send jsonBody
        
        If .Status = 200 Then
            TestSingleAPI = "✓ 连接成功"
        Else
            TestSingleAPI = "✗ 连接失败 (" & .Status & ")"
        End If
    End With
    
    Set http = Nothing
    Exit Function
    
TestError:
    TestSingleAPI = "✗ 测试错误: " & Err.Description
    If Not http Is Nothing Then Set http = Nothing
End Function
' ===== 
备用版本：使用注册表存储API密钥 =====
' 如果文档变量方式有问题，可以使用这些函数

' 从注册表加载API设置
Sub LoadAPISettingsFromRegistry()
    On Error Resume Next
    
    ' 从注册表读取API密钥
    openaiKey = GetSetting("WordAI", "APIKeys", "OpenAI", "")
    claudeKey = GetSetting("WordAI", "APIKeys", "Claude", "")
    qwenKey = GetSetting("WordAI", "APIKeys", "Qwen", "")
    
    On Error GoTo 0
End Sub

' 保存API设置到注册表
Sub SaveAPISettingsToRegistry()
    On Error Resume Next
    
    ' 保存到注册表
    SaveSetting "WordAI", "APIKeys", "OpenAI", openaiKey
    SaveSetting "WordAI", "APIKeys", "Claude", claudeKey
    SaveSetting "WordAI", "APIKeys", "Qwen", qwenKey
    
    On Error GoTo 0
End Sub

' 清除注册表中的API设置
Sub ClearAPISettingsFromRegistry()
    On Error Resume Next
    
    DeleteSetting "WordAI", "APIKeys", "OpenAI"
    DeleteSetting "WordAI", "APIKeys", "Claude"
    DeleteSetting "WordAI", "APIKeys", "Qwen"
    
    On Error GoTo 0
End Sub

' ===== 简化版本的主调用函数（使用注册表）=====
Sub AIDY_Registry(control As IRibbonControl)
    On Error GoTo AIErrorHandler
    
    ' 声明所有变量
    Dim prompt As String
    Dim aiResults As String
    Dim http As Object
    Dim jsonBody As String
    Dim response As String
    Dim i As Integer
    Dim startPos As Long
    Dim endPos As Long
    Dim content As String
    
    ' 从注册表加载API设置
    LoadAPISettingsFromRegistry
    
    ' 检查是否已配置API
    If openaiKey = "" And claudeKey = "" And qwenKey = "" Then
        If MsgBox("检测到未配置API密钥，是否现在配置？", vbYesNo + vbQuestion, "API配置") = vbYes Then
            ShowSimpleAPIConfig
        Else
            Exit Sub
        End If
    End If
    
    ' 获取用户输入的问题
    prompt = InputBox("请输入要发送给AI的问题：", "AI助手", "你好，请介绍一下自己")
    If prompt = "" Then GoTo AICallEnd
    
    aiResults = "AI调用结果：" & vbCrLf & String(50, "=") & vbCrLf & vbCrLf
    
    ' 调用OpenAI（如果已配置）
    If openaiKey <> "" Then
        Set http = CreateObject("MSXML2.XMLHTTP")
        jsonBody = "{""model"":""gpt-3.5-turbo"",""messages"":[{""role"":""user"",""content"":""" & _
                   Replace(Replace(Replace(prompt, "\", "\\"), """", "\"""), vbCrLf, "\n") & """}],""max_tokens"":1000}"
        
        With http
            .Open "POST", "https://api.openai.com/v1/chat/completions", False
            .setRequestHeader "Content-Type", "application/json"
            .setRequestHeader "Authorization", "Bearer " & openaiKey
            .send jsonBody
            
            If .Status = 200 Then
                response = .responseText
                startPos = InStr(response, """content"":""")
                If startPos > 0 Then
                    startPos = startPos + 11
                    endPos = InStr(startPos, response, """")
                    If endPos > startPos Then
                        content = Mid(response, startPos, endPos - startPos)
                        content = Replace(Replace(Replace(content, "\n", vbCrLf), "\""", """"), "\\", "\")
                        aiResults = aiResults & "【OpenAI】" & vbCrLf & content & vbCrLf & vbCrLf
                    End If
                End If
            Else
                aiResults = aiResults & "【OpenAI】调用失败 (状态码: " & .Status & ")" & vbCrLf & vbCrLf
            End If
        End With
        Set http = Nothing
    End If
    
    ' 显示结果
    If Len(aiResults) > 100 Then
        MsgBox aiResults, vbInformation, "AI调用结果"
    Else
        MsgBox "没有获取到AI响应，请检查API配置", vbExclamation, "提示"
    End If
    
AICallEnd:
    Exit Sub
    
AIErrorHandler:
    MsgBox "AI调用发生错误: " & Err.Description, vbExclamation, "错误"
    If Not http Is Nothing Then Set http = Nothing
    Resume AICallEnd
End Sub

' 简化的API配置界面
Sub ShowSimpleAPIConfig()
    Dim newKey As String
    
    ' 配置OpenAI
    newKey = InputBox("请输入OpenAI API Key：" & vbCrLf & vbCrLf & _
                     "获取地址：https://platform.openai.com/api-keys" & vbCrLf & _
                     "格式：sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", _
                     "OpenAI配置", openaiKey)
    If newKey <> "" Then
        openaiKey = newKey
        SaveAPISettingsToRegistry
        MsgBox "OpenAI API Key 配置成功！", vbInformation, "配置完成"
    End If
End Sub
' ===
== 推荐使用的简化版本 =====
' 使用注册表存储，避免文档变量问题

Sub AIDY_Simple(control As IRibbonControl)
    On Error GoTo AIErrorHandler
    
    ' 声明所有变量
    Dim prompt As String
    Dim aiResults As String
    Dim http As Object
    Dim jsonBody As String
    Dim response As String
    Dim startPos As Long
    Dim endPos As Long
    Dim content As String
    
    ' 从注册表加载API设置
    openaiKey = GetSetting("WordAI", "APIKeys", "OpenAI", "")
    claudeKey = GetSetting("WordAI", "APIKeys", "Claude", "")
    qwenKey = GetSetting("WordAI", "APIKeys", "Qwen", "")
    
    ' 检查是否已配置API
    If openaiKey = "" And claudeKey = "" And qwenKey = "" Then
        If MsgBox("检测到未配置API密钥，是否现在配置？", vbYesNo + vbQuestion, "API配置") = vbYes Then
            ConfigureAPISimple
        Else
            Exit Sub
        End If
    End If
    
    ' 获取用户输入的问题
    prompt = InputBox("请输入要发送给AI的问题：", "AI助手", "你好，请介绍一下自己")
    If prompt = "" Then GoTo AICallEnd
    
    aiResults = "AI调用结果：" & vbCrLf & String(50, "=") & vbCrLf & vbCrLf
    
    ' 调用OpenAI（如果已配置）
    If openaiKey <> "" Then
        Set http = CreateObject("MSXML2.XMLHTTP")
        jsonBody = "{""model"":""gpt-3.5-turbo"",""messages"":[{""role"":""user"",""content"":""" & _
                   Replace(Replace(Replace(prompt, "\", "\\"), """", "\"""), vbCrLf, "\n") & """}],""max_tokens"":1000}"
        
        With http
            .Open "POST", "https://api.openai.com/v1/chat/completions", False
            .setRequestHeader "Content-Type", "application/json"
            .setRequestHeader "Authorization", "Bearer " & openaiKey
            .send jsonBody
            
            If .Status = 200 Then
                response = .responseText
                startPos = InStr(response, """content"":""")
                If startPos > 0 Then
                    startPos = startPos + 11
                    endPos = InStr(startPos, response, """")
                    If endPos > startPos Then
                        content = Mid(response, startPos, endPos - startPos)
                        content = Replace(Replace(Replace(content, "\n", vbCrLf), "\""", """"), "\\", "\")
                        aiResults = aiResults & "【OpenAI】" & vbCrLf & content & vbCrLf & vbCrLf
                    End If
                End If
            Else
                aiResults = aiResults & "【OpenAI】调用失败 (状态码: " & .Status & ")" & vbCrLf & vbCrLf
            End If
        End With
        Set http = Nothing
    End If
    
    ' 调用Claude（如果已配置）
    If claudeKey <> "" Then
        Set http = CreateObject("MSXML2.XMLHTTP")
        jsonBody = "{""model"":""claude-3-sonnet-20240229"",""max_tokens"":1000,""messages"":[{""role"":""user"",""content"":""" & _
                   Replace(Replace(Replace(prompt, "\", "\\"), """", "\"""), vbCrLf, "\n") & """}]}"
        
        With http
            .Open "POST", "https://api.anthropic.com/v1/messages", False
            .setRequestHeader "Content-Type", "application/json"
            .setRequestHeader "Authorization", "Bearer " & claudeKey
            .setRequestHeader "anthropic-version", "2023-06-01"
            .send jsonBody
            
            If .Status = 200 Then
                response = .responseText
                startPos = InStr(response, """text"":""")
                If startPos > 0 Then
                    startPos = startPos + 8
                    endPos = InStr(startPos, response, """")
                    If endPos > startPos Then
                        content = Mid(response, startPos, endPos - startPos)
                        content = Replace(Replace(Replace(content, "\n", vbCrLf), "\""", """"), "\\", "\")
                        aiResults = aiResults & "【Claude】" & vbCrLf & content & vbCrLf & vbCrLf
                    End If
                End If
            Else
                aiResults = aiResults & "【Claude】调用失败 (状态码: " & .Status & ")" & vbCrLf & vbCrLf
            End If
        End With
        Set http = Nothing
    End If
    
    ' 调用通义千问（如果已配置）
    If qwenKey <> "" Then
        Set http = CreateObject("MSXML2.XMLHTTP")
        jsonBody = "{""model"":""qwen-turbo"",""input"":{""messages"":[{""role"":""user"",""content"":""" & _
                   Replace(Replace(Replace(prompt, "\", "\\"), """", "\"""), vbCrLf, "\n") & """}]},""parameters"":{""max_tokens"":1000}}"
        
        With http
            .Open "POST", "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation", False
            .setRequestHeader "Content-Type", "application/json"
            .setRequestHeader "Authorization", "Bearer " & qwenKey
            .setRequestHeader "X-DashScope-SSE", "disable"
            .send jsonBody
            
            If .Status = 200 Then
                response = .responseText
                startPos = InStr(response, """content"":""")
                If startPos > 0 Then
                    startPos = startPos + 11
                    endPos = InStr(startPos, response, """")
                    If endPos > startPos Then
                        content = Mid(response, startPos, endPos - startPos)
                        content = Replace(Replace(Replace(content, "\n", vbCrLf), "\""", """"), "\\", "\")
                        aiResults = aiResults & "【通义千问】" & vbCrLf & content & vbCrLf & vbCrLf
                    End If
                End If
            Else
                aiResults = aiResults & "【通义千问】调用失败 (状态码: " & .Status & ")" & vbCrLf & vbCrLf
            End If
        End With
        Set http = Nothing
    End If
    
    ' 显示结果
    If Len(aiResults) > 100 Then
        Dim choice As VbMsgBoxResult
        choice = MsgBox(Left(aiResults, 1000) & vbCrLf & vbCrLf & "是否将结果插入到当前文档？", _
                       vbYesNo + vbInformation, "AI调用结果")
        If choice = vbYes Then
            Selection.TypeText aiResults
        End If
    Else
        MsgBox "没有获取到AI响应，请检查API配置", vbExclamation, "提示"
    End If
    
AICallEnd:
    Exit Sub
    
AIErrorHandler:
    MsgBox "AI调用发生错误: " & Err.Description, vbExclamation, "错误"
    If Not http Is Nothing Then Set http = Nothing
    Resume AICallEnd
End Sub

' 简化的API配置界面
Sub ConfigureAPISimple()
    Dim choice As String
    Dim newKey As String
    
    ' 显示配置菜单
    choice = InputBox("请选择要配置的API：" & vbCrLf & vbCrLf & _
                     "1. OpenAI" & vbCrLf & _
                     "2. Claude" & vbCrLf & _
                     "3. 通义千问" & vbCrLf & vbCrLf & _
                     "请输入数字 (1-3)：", "API配置", "1")
    
    Select Case choice
        Case "1"
            newKey = InputBox("请输入OpenAI API Key：" & vbCrLf & vbCrLf & _
                             "获取地址：https://platform.openai.com/api-keys" & vbCrLf & _
                             "格式：sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", _
                             "OpenAI配置", GetSetting("WordAI", "APIKeys", "OpenAI", ""))
            If newKey <> "" Then
                SaveSetting "WordAI", "APIKeys", "OpenAI", newKey
                MsgBox "OpenAI API Key 配置成功！", vbInformation, "配置完成"
            End If
            
        Case "2"
            newKey = InputBox("请输入Claude API Key：" & vbCrLf & vbCrLf & _
                             "获取地址：https://console.anthropic.com/" & vbCrLf & _
                             "格式：sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", _
                             "Claude配置", GetSetting("WordAI", "APIKeys", "Claude", ""))
            If newKey <> "" Then
                SaveSetting "WordAI", "APIKeys", "Claude", newKey
                MsgBox "Claude API Key 配置成功！", vbInformation, "配置完成"
            End If
            
        Case "3"
            newKey = InputBox("请输入通义千问 API Key：" & vbCrLf & vbCrLf & _
                             "获取地址：https://dashscope.console.aliyun.com/" & vbCrLf & _
                             "格式：sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx", _
                             "通义千问配置", GetSetting("WordAI", "APIKeys", "Qwen", ""))
            If newKey <> "" Then
                SaveSetting "WordAI", "APIKeys", "Qwen", newKey
                MsgBox "通义千问 API Key 配置成功！", vbInformation, "配置完成"
            End If
    End Select
    
    ' 询问是否继续配置
    If MsgBox("是否继续配置其他API？", vbYesNo + vbQuestion, "继续配置") = vbYes Then
        ConfigureAPISimple
    End If
End Sub