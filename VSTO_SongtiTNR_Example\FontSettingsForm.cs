using System;
using System.Drawing;
using System.Windows.Forms;

namespace WordFontFormatterAddIn
{
    /// <summary>
    /// 字体设置窗体 - 替代 VBA 的 InputBox
    /// </summary>
    public partial class FontSettingsForm : Form
    {
        public FontSettings Settings { get; private set; }

        public FontSettingsForm()
        {
            InitializeComponent();
            LoadDefaultSettings();
        }

        private void InitializeComponent()
        {
            this.Text = "字体格式设置";
            this.Size = new Size(400, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // 创建控件
            CreateControls();
        }

        private void CreateControls()
        {
            int yPos = 20;
            int labelWidth = 80;
            int controlWidth = 120;
            int spacing = 35;

            // 编号字体设置组
            var numberGroupBox = new GroupBox
            {
                Text = "编号字体设置",
                Location = new Point(10, yPos),
                Size = new Size(360, 120)
            };
            this.Controls.Add(numberGroupBox);

            // 编号字体名称
            var lblNumberFont = new Label { Text = "字体名称:", Location = new Point(10, 25), Size = new Size(labelWidth, 20) };
            var cmbNumberFont = new ComboBox 
            { 
                Name = "cmbNumberFont",
                Location = new Point(100, 23), 
                Size = new Size(controlWidth, 20),
                DropDownStyle = ComboBoxStyle.DropDown
            };
            cmbNumberFont.Items.AddRange(new[] { "宋体", "黑体", "楷体", "仿宋", "微软雅黑", "Times New Roman", "Arial" });
            
            // 编号字体大小
            var lblNumberSize = new Label { Text = "字体大小:", Location = new Point(10, 55), Size = new Size(labelWidth, 20) };
            var numNumberSize = new NumericUpDown 
            { 
                Name = "numNumberSize",
                Location = new Point(100, 53), 
                Size = new Size(controlWidth, 20),
                DecimalPlaces = 1,
                Minimum = 6,
                Maximum = 72,
                Value = 10.5m
            };

            // 编号字体粗体
            var chkNumberBold = new CheckBox 
            { 
                Name = "chkNumberBold",
                Text = "粗体", 
                Location = new Point(100, 85), 
                Size = new Size(controlWidth, 20) 
            };

            numberGroupBox.Controls.AddRange(new Control[] { lblNumberFont, cmbNumberFont, lblNumberSize, numNumberSize, chkNumberBold });

            yPos += 140;

            // 正文字体设置组
            var textGroupBox = new GroupBox
            {
                Text = "正文字体设置",
                Location = new Point(10, yPos),
                Size = new Size(360, 120)
            };
            this.Controls.Add(textGroupBox);

            // 正文字体名称
            var lblTextFont = new Label { Text = "字体名称:", Location = new Point(10, 25), Size = new Size(labelWidth, 20) };
            var cmbTextFont = new ComboBox 
            { 
                Name = "cmbTextFont",
                Location = new Point(100, 23), 
                Size = new Size(controlWidth, 20),
                DropDownStyle = ComboBoxStyle.DropDown
            };
            cmbTextFont.Items.AddRange(new[] { "宋体", "黑体", "楷体", "仿宋", "微软雅黑", "Times New Roman", "Arial" });

            // 正文字体大小
            var lblTextSize = new Label { Text = "字体大小:", Location = new Point(10, 55), Size = new Size(labelWidth, 20) };
            var numTextSize = new NumericUpDown 
            { 
                Name = "numTextSize",
                Location = new Point(100, 53), 
                Size = new Size(controlWidth, 20),
                DecimalPlaces = 1,
                Minimum = 6,
                Maximum = 72,
                Value = 10.5m
            };

            // 正文字体粗体
            var chkTextBold = new CheckBox 
            { 
                Name = "chkTextBold",
                Text = "粗体", 
                Location = new Point(100, 85), 
                Size = new Size(controlWidth, 20) 
            };

            textGroupBox.Controls.AddRange(new Control[] { lblTextFont, cmbTextFont, lblTextSize, numTextSize, chkTextBold });

            yPos += 140;

            // 按钮
            var btnOK = new Button 
            { 
                Text = "确定", 
                Location = new Point(220, yPos), 
                Size = new Size(75, 25),
                DialogResult = DialogResult.OK
            };
            btnOK.Click += BtnOK_Click;

            var btnCancel = new Button 
            { 
                Text = "取消", 
                Location = new Point(305, yPos), 
                Size = new Size(75, 25),
                DialogResult = DialogResult.Cancel
            };

            this.Controls.AddRange(new Control[] { btnOK, btnCancel });

            // 说明标签
            var lblNote = new Label
            {
                Text = "注意：数字将自动设置为 Times New Roman 字体",
                Location = new Point(10, yPos + 35),
                Size = new Size(360, 20),
                ForeColor = Color.Blue
            };
            this.Controls.Add(lblNote);
        }

        private void LoadDefaultSettings()
        {
            // 设置默认值
            var cmbNumberFont = this.Controls.Find("cmbNumberFont", true)[0] as ComboBox;
            var cmbTextFont = this.Controls.Find("cmbTextFont", true)[0] as ComboBox;
            
            cmbNumberFont.Text = "宋体";
            cmbTextFont.Text = "宋体";
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            try
            {
                // 收集用户输入
                var cmbNumberFont = this.Controls.Find("cmbNumberFont", true)[0] as ComboBox;
                var numNumberSize = this.Controls.Find("numNumberSize", true)[0] as NumericUpDown;
                var chkNumberBold = this.Controls.Find("chkNumberBold", true)[0] as CheckBox;
                
                var cmbTextFont = this.Controls.Find("cmbTextFont", true)[0] as ComboBox;
                var numTextSize = this.Controls.Find("numTextSize", true)[0] as NumericUpDown;
                var chkTextBold = this.Controls.Find("chkTextBold", true)[0] as CheckBox;

                Settings = new FontSettings
                {
                    NumberFontName = cmbNumberFont.Text,
                    NumberFontSize = (double)numNumberSize.Value,
                    NumberIsBold = chkNumberBold.Checked,
                    TextFontName = cmbTextFont.Text,
                    TextFontSize = (double)numTextSize.Value,
                    TextIsBold = chkTextBold.Checked
                };

                if (!Settings.IsValid())
                {
                    MessageBox.Show("请填写完整的字体设置信息！", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置保存失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}