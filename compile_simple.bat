@echo off
echo 简单编译脚本 - Word格式化工具

REM 检查源文件
if not exist "WordFormatter_Clean.cpp" (
    echo 错误：找不到源文件 WordFormatter_Clean.cpp
    pause
    exit /b 1
)

echo 正在尝试编译...

REM 方法1：尝试使用g++（MinGW）
where g++ >nul 2>&1
if %errorlevel% == 0 (
    echo 使用g++编译器...
    g++ -std=c++17 -O2 -DUNICODE -D_UNICODE -D_WIN32_DCOM -D_CRT_SECURE_NO_WARNINGS ^
        WordFormatter_Clean.cpp ^
        -lole32 -loleaut32 -luuid -luser32 -lkernel32 ^
        -o WordFormatter.exe
    
    if exist WordFormatter.exe (
        echo g++编译成功！
        goto :success
    ) else (
        echo g++编译失败，尝试其他方法...
    )
)

REM 方法2：尝试使用cl（Visual Studio）
where cl >nul 2>&1
if %errorlevel% == 0 (
    echo 使用Visual Studio编译器...
    cl /EHsc /std:c++17 /utf-8 ^
       /D_WIN32_DCOM /DUNICODE /D_UNICODE /D_CRT_SECURE_NO_WARNINGS ^
       WordFormatter_Clean.cpp ^
       /link ole32.lib oleaut32.lib uuid.lib user32.lib kernel32.lib ^
       /OUT:WordFormatter.exe
    
    if exist WordFormatter.exe (
        echo Visual Studio编译成功！
        goto :success
    ) else (
        echo Visual Studio编译失败...
    )
)

REM 方法3：尝试查找并设置Visual Studio环境
echo 查找Visual Studio环境...

REM 常见的Visual Studio路径
set VS_PATHS[0]="C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
set VS_PATHS[1]="C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
set VS_PATHS[2]="C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat"
set VS_PATHS[3]="C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
set VS_PATHS[4]="C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat"
set VS_PATHS[5]="C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\VC\Auxiliary\Build\vcvars64.bat"

for /L %%i in (0,1,5) do (
    call set "VS_PATH=%%VS_PATHS[%%i]%%"
    call set "VS_PATH=%%VS_PATH:"=%%"
    if exist "!VS_PATH!" (
        echo 找到Visual Studio: !VS_PATH!
        call "!VS_PATH!" >nul 2>&1
        
        cl /EHsc /std:c++17 /utf-8 ^
           /D_WIN32_DCOM /DUNICODE /D_UNICODE /D_CRT_SECURE_NO_WARNINGS ^
           WordFormatter_Clean.cpp ^
           /link ole32.lib oleaut32.lib uuid.lib user32.lib kernel32.lib ^
           /OUT:WordFormatter.exe
        
        if exist WordFormatter.exe (
            echo Visual Studio编译成功！
            goto :success
        )
    )
)

REM 如果所有方法都失败
echo.
echo ========================================
echo 编译失败！
echo ========================================
echo.
echo 请安装以下任一编译器：
echo.
echo 1. MinGW-w64 (推荐)
echo    下载地址: https://www.mingw-w64.org/downloads/
echo    或使用: winget install mingw
echo.
echo 2. Visual Studio Community (免费)
echo    下载地址: https://visualstudio.microsoft.com/downloads/
echo.
echo 3. Visual Studio Build Tools
echo    下载地址: https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2022
echo.
goto :end

:success
echo.
echo ========================================
echo 编译成功！
echo ========================================
echo.
echo 可执行文件: WordFormatter.exe
echo.
echo 使用方法：
echo 1. 打开Word文档
echo 2. 选择需要格式化的文本  
echo 3. 运行 WordFormatter.exe
echo 4. 按提示输入格式参数
echo.
echo 参数示例: 宋体,10.5,否,宋体,10.5,否

:end
pause