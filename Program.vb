Imports System.Windows.Forms
Imports System.Drawing
Imports Word = Microsoft.Office.Interop.Word

Module Program
    <STAThread>
    Sub Main()
        Try
            ' 启用视觉样式
            Application.EnableVisualStyles()
            Application.SetCompatibleTextRenderingDefault(False)
            
            ' 显示启动消息
            MessageBox.Show("正在启动Word文档处理工具...", "启动", MessageBoxButtons.OK, MessageBoxIcon.Information)
            
            ' 尝试创建Word应用程序
            Dim wordApp As Word.Application = Nothing
            Try
                wordApp = New Word.Application()
                wordApp.Visible = True
                MessageBox.Show("Word连接成功！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Catch ex As Exception
                MessageBox.Show($"Word连接失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Finally
                If wordApp IsNot Nothing Then
                    wordApp.Quit()
                    wordApp = Nothing
                End If
            End Try
            
            ' 创建简单的测试窗体
            Dim testForm As New Form()
            testForm.Text = "Word文档处理工具 - 测试版"
            testForm.Size = New Size(400, 300)
            testForm.StartPosition = FormStartPosition.CenterScreen
            
            Dim lblTest As New Label()
            lblTest.Text = "测试程序运行成功！" & vbCrLf & "Word连接正常。"
            lblTest.Location = New Point(50, 50)
            lblTest.Size = New Size(300, 100)
            testForm.Controls.Add(lblTest)
            
            Dim btnClose As New Button()
            btnClose.Text = "关闭"
            btnClose.Location = New Point(150, 200)
            btnClose.Size = New Size(100, 30)
            AddHandler btnClose.Click, Sub() testForm.Close()
            testForm.Controls.Add(btnClose)
            
            Application.Run(testForm)
            
        Catch ex As Exception
            MessageBox.Show($"程序启动失败: {ex.Message}{vbCrLf}{vbCrLf}详细信息:{vbCrLf}{ex.ToString()}", 
                          "严重错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub
End Module