Attribute VB_Name = "ModifiedTableFormat"
Sub FormatActiveTable()
    ' 本宏用于格式化当前选中的表格
    ' 作者：Cline
    ' 版本：2.0 (根据用户反馈优化)
    ' 功能：
    ' 1. 统一设置行高、边距。
    ' 2. 根据单元格位置和内容（标题、合计、数字、百分比、中文）智能设置对齐方式和格式。
    ' 3. 移除了不稳定的 .Select/.Selection 写法，代码更健壮。

    Dim mytable As Table
    Dim cellText As String
    Dim i As Long, j As Long
    
    ' 检查是否选中了表格
    If Selection.Information(wdWithInTable) = False Then
        MsgBox "请将光标置于要格式化的表格内。", vbExclamation, "操作无效"
        Exit Sub
    End If
    
    Set mytable = Selection.Tables(1)
    
    With mytable
        On Error Resume Next  ' 保留错误处理，以跳过合并单元格中不存在的单元格
        
        ' 预先判断哪些列包含百分比标题，用于后续判断
        Dim hasPercentColumn() As Boolean
        ReDim hasPercentColumn(1 To .Columns.Count)
        For j = 1 To .Columns.Count
            If InStr(.cell(1, j).Range.text, "%") > 0 Then
                hasPercentColumn(j) = True
            End If
        Next j
        
        For i = 1 To .Rows.Count
            For j = 1 To .Columns.Count
                ' 获取并清理单元格文本
                cellText = Left(.cell(i, j).Range.text, Len(.cell(i, j).Range.text) - 2)
                
                ' 设置基础段落格式
                With .cell(i, j).Range.ParagraphFormat
                    .SpaceBefore = 0: .SpaceAfter = 0
                    .FirstLineIndent = 0: .LeftIndent = 0: .RightIndent = 0
                    .LineSpacingRule = wdLineSpaceExactly: .LineSpacing = 16
                    .AutoAdjustRightIndent = False
                End With
                
                ' 设置垂直居中（全局应用）
                .cell(i, j).VerticalAlignment = wdCellAlignVerticalCenter
                
                ' =================================================================
                ' === 核心修改：采用第一部分代码的扁平化、健壮的逻辑结构 ===
                ' =================================================================
                
                Dim cleanedText As String
                cleanedText = Replace(cellText, ",", "")  ' 移除千分位逗号
                
                ' 判断是否包含中文
                Dim isChinese As Boolean
                isChinese = False
                Dim k As Integer
                For k = 1 To Len(cellText)
                    If AscW(Mid(cellText, k, 1)) > 127 Then
                        isChinese = True
                        Exit For
                    End If
                Next k

                ' --- 开始对齐和格式化判断 ---
                If i = 1 Then
                    ' 规则1: 第一行 (标题行) -> 居中加粗
                    .cell(i, j).Range.Font.Bold = True
                    .cell(i, j).Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                    
                ElseIf j = 1 And i = .Rows.Count Then
                    ' 规则2: 第一列的最后一行 (通常是"合计") -> 居中
                    .cell(i, j).Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                    
                ElseIf j = 1 Then
                    ' 规则3: 第一列的其他行 -> 数字居中，文本左对齐
                    If IsNumeric(cleanedText) And Len(Trim(cellText)) > 0 Then
                        .cell(i, j).Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                        .cell(i, j).WordWrap = False
                    Else
                        .cell(i, j).Range.ParagraphFormat.Alignment = wdAlignParagraphLeft
                    End If
                    
                ElseIf isChinese Then
                    ' 规则4: 其他列的中文内容 -> 左对齐
                    .cell(i, j).Range.ParagraphFormat.Alignment = wdAlignParagraphLeft
                    .cell(i, j).WordWrap = True
                    .cell(i, j).Range.NoProofing = True
                    
                ElseIf InStr(cellText, "%") > 0 Then
                    ' 规则5: 包含百分号的单元格 -> 居中
                    .cell(i, j).Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                    
                ElseIf hasPercentColumn(j) And IsNumeric(cleanedText) And Len(Trim(cellText)) > 0 Then
                    ' 规则6: 百分比列下的数字 -> 居中
                    .cell(i, j).Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                    .cell(i, j).WordWrap = False
                    
                ElseIf IsNumeric(cleanedText) And Len(Trim(cellText)) > 0 Then
                    ' 规则7: 其他所有数字 -> 右对齐
                    .cell(i, j).Range.ParagraphFormat.Alignment = wdAlignParagraphRight
                    .cell(i, j).WordWrap = False
                    
                Else
                    ' 规则8: 其他所有情况 (如英文、特殊符号等) -> 居中
                    .cell(i, j).Range.ParagraphFormat.Alignment = wdAlignParagraphCenter
                End If
                
                ' 最后一行加粗 (此逻辑在循环内，会覆盖之前的设置，确保最后一行全部加粗)
                If i = .Rows.Count Then
                    .cell(i, j).Range.Font.Bold = True
                End If
                
                Err.Clear  ' 清除错误，继续下一个单元格
            Next j
        Next i
    End With
    
    Set mytable = Nothing
    MsgBox "表格格式化完成！", vbInformation, "操作成功"
    
End Sub
